/**
 * @file 飞书机器人通知
 */

'use strict';

import { Service } from 'egg';

export interface IServer {
  name: string;
  ip?: string;
  port?: string;
}

export interface IContent {
  title?: string;
  text?: string;
}

/**
 * receive_id
 *1: 'https://open.feishu.cn/open-apis/bot/v2/hook/72d194f6-3920-49f6-8069-9aa9bbf78214',  # 机器人问题反馈群
 *2: 'https://open.feishu.cn/open-apis/bot/v2/hook/56f03c4d-0d56-4f10-a705-3b3078739b70',  # 方正问题反馈群
 *3: 'https://open.feishu.cn/open-apis/bot/v2/hook/4f196a2b-4840-44aa-ba89-d50edf5c21e6',  # 长沙运营降 10 倍！群
 *4: 'https://open.feishu.cn/open-apis/bot/v2/hook/1b0af03c-5470-453d-98bc-8501cd632a04',  # 合心研发群
 *5: 'https://open.feishu.cn/open-apis/bot/v2/hook/842cc932-0adc-4503-ab89-8e76ad7d6377',  # 数据一致性监控群
 *6: 'https://open.feishu.cn/open-apis/bot/v2/hook/f9ea5d54-d90e-4dca-bc66-447f452a8f68',  # FBD P0 异常无法返回
 *7: 'https://open.feishu.cn/open-apis/bot/v2/hook/3d7fd73d-940b-4a65-b296-b11826d74e3c',  # 临时 | 工单系统实测
 *8: 'https://open.feishu.cn/open-apis/bot/v2/hook/12b6e030-2c9f-4a81-ae74-be79de8c43b0',  # VSTO 全对
 *9: 'https://open.feishu.cn/open-apis/bot/v2/hook/87af4e8d-8971-424c-b262-e2f11291a322',  # VSTO 错 3 页内
 *10: 'https://open.feishu.cn/open-apis/bot/v2/hook/37aa9679-ca65-467f-92c4-0bc699de64c4',  # PPT小样把关群
 *11: 'https://open.feishu.cn/open-apis/bot/v2/hook/91cadfca-2781-493c-99c4-6448c6f3334c',  # PPT 自动化群
 *12: 'https://open.feishu.cn/open-apis/bot/v2/hook/d66ff18e-0837-4858-96a6-1d4005697b7d',  # vba_runner 问题反馈
 *13: 'https://open.feishu.cn/open-apis/bot/v2/hook/43a02113-3500-4f4b-a12a-7f147310fb39',  # word / fbd 预处理反馈
 *14: 'https://open.feishu.cn/open-apis/bot/v2/hook/4824b284-b78b-458b-816d-7d9c5508d23b',  # 表格布局优化
 *15: 'https://open.feishu.cn/open-apis/bot/v2/hook/5777e2f9-a028-4482-9f92-22d2e4b886d8',  # 加机器群
 *16: 'https://open.feishu.cn/open-apis/bot/v2/hook/3ed6b764-1221-4300-b0d3-bd131d04e2f6',  # Word2FBD 小组
 *17: 'https://open.feishu.cn/open-apis/bot/v2/hook/6a39321a-64ee-41c6-93eb-d37cae1350bc',  # 学转教小队
 *18: 'https://open.feishu.cn/open-apis/bot/v2/hook/4d6d8a14-f057-45b5-9fcb-59dca03649d3',  # 新建工单通知
 */
export interface IRootObject {
  type: string;
  receive_id: number;
  server: IServer;
  content: IContent;
}

export default class RobotService extends Service {
  public async sendRobotMessage(data: IRootObject) {
    const { app } = this;
    return await app.curl(
      // 'http://172.16.210.210/api/message/send/default',
      'http://robot.hexinedu.com/api/message/send/default',
      {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify(data),
      }
    );
  }

  public async sendRobotMessageInTask(taskId: number) {
    await this.app.curl('http://robot.hexinedu.com/api/message/send/default', {
      method: 'POST',
      dataType: 'json',
      data: JSON.stringify({
        type: 'error',
        receive_id: 20,
        server: { name: '任务创建卡图片转存' },
        content: {
          title: '任务创建图片转存卡住了！',
          text: `请速速联系相关同学！\n[taskId] is ${taskId}`,
        },
      }),
    });
  }

  public async sendRobotMessageWhenTaskReviewed(taskId, bookId) {
    if (!bookId) {
      return this.logger.info(`[generateFormattedData] taskId=${taskId} 任务不在任何图书内`);
    }
    const book = await this.service.book.getOne({ where: { id: bookId } });
    if (!book) {
      return this.logger.info(`[generateFormattedData] taskId=${taskId} 任务对应的图书不存在`);
    }
    const projectId = book.projectId;
    if (!projectId) {
      return this.logger.info(`[generateFormattedData] taskId=${taskId} bookId=${bookId} 图书不在任何项目内`);
    }
    const project = await this.service.project.base.getRelateOne({ where: { id: projectId } });
    if (!project) {
      return this.logger.info(`[generateFormattedData] taskId=${taskId} projectId=${projectId} 任务对应的图书不存在`);
    }
    const projectMeta = await this.service.project.meta.getMetas({ projectId });
    if (
      project.books?.question?.complete &&
      project.books.question.complete === project.books.question.count &&
      (project.appKey === 'bc8f35108bf3515465e99f36' || projectMeta.isAIEdit)
    ) {
      // 只对万唯客户的项目进行通知
      await this.service.robot.sendRobotMessage({
        type: 'info',
        receive_id: 18,
        content: {
          title: '任务审核完成',
          text: `项目：${project.projectName}（id：${project.id}）全部子任务审核完成，请继续推进！`,
        },
        server: { name: 'Xdoc 线上服务' },
      });
      return;
    }
  }

  public async sendRobotMessageWhenCallbackError(taskId, reason) {
    await this.app.curl('http://robot.hexinedu.com/api/message/send/default', {
      method: 'POST',
      dataType: 'json',
      data: JSON.stringify({
        type: 'error',
        receive_id: 21, // html_docx/w_ppt报警群
        server: { name: '图片转录回调' },
        content: {
          title: '服务 图片转录回调 WORD生成失败！',
          text: `请速速联系相关同学！\n[error] is ${reason}, \n[taskId] is ${taskId}`,
        },
      }),
    });
  }
}
