'use strict';

import { Service } from 'egg';
import { flushHTML } from '../core/utils/htmlHelper';
import { formatJSONInAI, iterateNode } from '../core/utils/treeHelper';
import WordAnnotationMannger from '../core/utils/aiEdit/WordAnnotationMannger';
import { extractSymbolId } from '../core/utils/jsonHelper/check';
import { errorNodeStat } from '../core/utils/jsonHelper';
import { preCleanHtml } from '../core/utils/htmlToJsonV4/helper/cleanupNode';
import { cleanJsonNodes } from '../core/utils/htmlToJsonV4';
import axios from 'axios';

export default class OperationService extends Service {

  // 字段名映射
  private static readonly FIELD_LABELS: Record<string, string> = {
    taskId: '任务ID',
    appKey: 'appKey',
    subject: '学科',
    stage: '学段',
    wordType: '任务类型',
    htmlUrl: '修复后HTML地址',
    jsonUrl: '修复后JSON地址',
    wordUrl: '初始Word地址',
    jsonResultUrl: '修复后JSON地址',
    cleanedHtmlUrl: '清洗后HTML地址',
  };

  // 学科映射
  private static readonly SUBJECT_LABELS: Record<string, string> = {
    math: '数学',
    chinese: '语文',
    english: '英语',
    physics: '物理',
    chemistry: '化学',
    biology: '生物',
    history: '历史',
    geography: '地理',
    politics: '政治',
    daode_fazhi: '道德与法治',
    computer_science: '计算机科学',
    science: '科学',
  };

  // 学段映射
  private static readonly STAGE_LABELS: Record<string, string> = {
    junior: '初中',
    senior: '高中',
    primary: '小学',
  };

  // 任务类型映射
  private static readonly WORD_TYPE_LABELS: Record<string, string> = {
    'wps-enhance_analysis': 'WPS增强解析',
    'wps-analysis': 'WPS解析',
    'wps-check': 'WPS校对',
    'enhance_analysis': 'WPS增强解析',
    'analysis': 'WPS解析',
    'check': 'WPS校对',
  };

  /**
   * 格式化任务参数为易读字符串
   * @param params 任务参数对象
   */
  formatTaskParams(params: Record<string, any>): string {
    try {
      if (!params || typeof params !== 'object') {
        return '';
      }

      const formattedParams: string[] = [];

      Object.entries(params).forEach(([key, value]) => {
        if (value === undefined || value === null) {
          return;
        }

        const label = OperationService.FIELD_LABELS[key] || key;
        let displayValue = String(value);

        // 特殊字段处理
        switch (key) {
        case 'subject':
          displayValue = OperationService.SUBJECT_LABELS[value as string] || value;
          break;
        case 'appKey':
          displayValue = value;
          break;
        case 'stage':
          displayValue = OperationService.STAGE_LABELS[value as string] || value;
          break;
        case 'wordType':
          // 检查所有可能的任务类型
          for (const [type, label] of Object.entries(OperationService.WORD_TYPE_LABELS)) {
            if (value.includes && value.includes(type)) {
              displayValue = label;
              break;
            }
          }
          if (displayValue === String(value)) {
            displayValue = OperationService.WORD_TYPE_LABELS[value as string] || value;
          }
          break;
        case 'htmlUrl':
        case 'jsonUrl':
        case 'wordUrl':
        case 'jsonResultUrl':
        case 'cleanedHtmlUrl':
          // URL只显示最后一部分，避免过长
          displayValue = value || '';
          break;
        }

        formattedParams.push(`${label}：${displayValue}`);
      });

      return formattedParams.join('\n');
    } catch (e) {
      this.logger.info(`formatTaskParams error: ${JSON.stringify(e)}`);
      return JSON.stringify(params);
    }
  }

  cleanHTMLSymbol(html: string): string {
    return flushHTML(html);
  }

  async formatJSON(json: any[], type: 'analysis' | 'check' = 'analysis') {
    return await formatJSONInAI(json, type);
  }

  /**
   * 带重试机制的HTTP请求方法
   * @param url 请求URL
   * @param config axios请求配置
   * @param maxRetries 最大重试次数，默认3次
   * @param retryDelay 重试延迟时间（毫秒），默认100ms
   */
  async httpGetWithRetry(
    url: string,
    config: any = {},
    maxRetries = 3,
    retryDelay = 500
  ) {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios.get(url, config);
        return response;
      } catch (error) {
        lastError = error;
        this.logger.warn(`[httpGetWithRetry] 第${attempt}次请求失败: ${url}, 错误: ${error.message}`);

        if (attempt < maxRetries) {
          // 如果不是最后一次尝试，等待延迟后重试
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }

    this.logger.error(`[httpGetWithRetry] 请求最终失败，已重试${maxRetries}次: ${url}, 最后错误: ${lastError.message}`);
    throw lastError;
  }

  async callbackTokens(taskId: string, key: string, tokens: number, stat: any = {}) {
    await this.app.curl(
      `${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/ticket/v1/update/model_cost`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket_id: taskId,
          key,
          cost_token: tokens,
          detail: stat,
        }),
      });

  }

  async autoPublishTask(task: any) {
    const { service, logger } = this;
    const taskId = task.taskId;
    const bookId = task.bookId;
    const html = await service.task.base.getOssData(task.appKey, taskId, 'html');
    const json = await service.task.base.convert2Json(task.appKey, taskId, html);
    const catalog = service.task.base.getCatalog(task.appKey, taskId, json);
    const errorStat = errorNodeStat(json);
    await service.task.base.setOssData(task.appKey, taskId, 'html', preCleanHtml(html));
    await service.task.base.setOssData(task.appKey, taskId, 'internal.json', json);
    await service.task.base.setOssData(task.appKey, taskId, 'json', cleanJsonNodes(json));
    await service.task.base.formateAndUploadDiffFile(task.appKey, taskId, html);
    const machineHtml = await service.task.base.getOssData(task.appKey, taskId, 'machine.html');
    const diffCharCount = html.length - machineHtml.length;
    await service.task.base.update(
      { diffCharCount },
      { where: { taskId } }
    );

    service.task.base.update(
      {
        status: service.task.base.statuses.reviewed,
        endReviewTime: new Date(),
        errorNodeCount: errorStat.count,
        catalog: catalog.length ? JSON.stringify(catalog) : '',
      },
      { where: { taskId } }
    );

    logger.info(`/taskV2/reviewConfirm 数据清洗回调 task:${taskId}`);
    const book = await service.book.getOne({ where: { id: task.bookId } });
    const bookAppKey = book!.appKey;
    await service.book.combineAndUploadByTaskIds(bookAppKey, bookId, [task]);
    await service.book.update(
      { status: service.book.statuses.reviewed, endReviewTime: new Date() },
      { where: { id: bookId } }
    );
    await service.task.history.create({
      userId: 10,
      taskId,
      type: service.task.history.taskV2Types.reviewConfirm.id,
      costTime: 0,
      data: '自动过',
    });

    // 感觉不需要都清洗一遍，最后发布的时候再全部清洗一下
    // await service.subjectScript.runSubjectScript(taskId, true);
    // const meta = await service.task.meta.getMetas({ taskId });
    // this.logger.info(`${taskId} metas :${JSON.stringify(meta)}`);
    // this.logger.info(`${taskId} pdfCount :${pdfCount}`);
    // 加入待统计列表
    await service.task.stat.push(task.taskId);
    this.logger.info(`${taskId} push`);
    await service.task.stat.newStat({
      html,
      taskId,
      type: 'review',
      appKey: task.appKey,
      subject: task.subject,
      imageCount: 0,
      pdfCount: 0,
      resourceType: task.resourceType!,
      cost: 0,
      userId: 0,
    });
    this.logger.info(`${taskId} stat`);
    logger.info(` /taskV2/reviewConfirm runningInback task:${JSON.stringify(taskId)}`);

  }

  // @todo: 待删除, 临时支持一下
  processWordAfterFixed(mannger: WordAnnotationMannger, json: any[]) {
    for (const { node } of iterateNode(json)) {
      node.error_info.forEach(({ id, error_info, fix_info, level, keywords }) => {
        let _id = id;
        if (!id) {
          // 如果没有 id，就找到题干中的 symbol
          if (node.content?.body) {
            _id = extractSymbolId(node.content.body);
          }
          if (!_id && node.content?.choices?.length) {
            _id = extractSymbolId(node.content.choices.map((v: { option: any; }) => v.option).join(''));
          }
          if (_id && node.content.answer.filter((v) => v.replace(/&nbsp;/g, '').trim()).length) {
            _id = extractSymbolId(node.content.answer[0]);
          }
        }
        mannger.addCommentToElementById(
          _id,
          error_info,
          fix_info,
          level,
          {
            author: '系统检查',
            initials: 'SYS',
            targetText: keywords,
          }
        );
      });
    }
  }
}
