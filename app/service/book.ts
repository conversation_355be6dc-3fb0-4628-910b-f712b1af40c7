/**
 * @file book
 * <AUTHOR>
 */

'use strict';

import { Context } from 'egg';
import ModelService from '../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../model/book';
import superSequelize from '../../typings/app/core/modelService';
import * as sha1 from 'sha1';
import { getHtml, parseHtml } from '../core/utils/htmlHelper';
import { iterateNode } from '../core/utils/treeHelper';
import { Op } from 'sequelize';
import { htmlToJsonV5 } from '../core/utils/aiEdit/htmlToJsonAI';

type supportExtension = 'json' | 'html' | 'docx' | 'catalog.json';

export default class ImageService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Book, defineAttributes);
  }

  public statuses = {
    processing: 11,
    unreviewed: 12,
    reviewing: 13,
    reviewed: 14,
    error: -1,
  };

  public types = {
    catalog: 1,
    question: 2,
  };

  private hashBookId(appKey: string, bookId: number) {
    return sha1(`${appKey}:${bookId}`);
  }

  public getUrl(appKey: string, bookId: number, extension: supportExtension, timestamp = true, internal: boolean | undefined = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}open/${appKey}/book/${this.hashBookId(appKey, bookId)}.${extension}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getOssData(appKey: string, bookId: number, extension: supportExtension) {
    const url = this.getUrl(appKey, bookId, extension, true, true);
    return this.service.oss.fetch(url, extension);
  }

  public async setOssData(appKey: string, bookId: number, extension: supportExtension, data: string | object) {
    const key = `open/${appKey}/book/${this.hashBookId(appKey, bookId)}.${extension}`;
    const url = await this.service.oss.upload(key, data, 'string');
    return url;
  }

  public async combineAndUploadByTaskIds(
    appKey: string,
    bookId: number,
    tasks: { taskId: number; appKey: string }[]
  ) {
    const { service } = this;
    const htmls = await Promise.all(tasks.map(async(task) => {
      let html = await service.task.base.getOssData(task.appKey, task.taskId, 'html');
      if (/data:image\/(.*?);base64,/ig.test(html)) {
        const nodes = parseHtml(html);
        if (await this.service.oss.uploadBase64forHtml(`open/${appKey}/task/image/tmp/`, nodes)) {
          html = getHtml(nodes);
          await service.task.base.setOssData(task.appKey, task.taskId, 'html', html);
        }
      }
      return html;
    }));
    const html = htmls.join('\n');
    await this.setOssData(appKey, bookId, 'html', html);
    return html;
  }

  public async combineJsonByTaskIds(tasks: { taskId: number; appKey: string }[]) {
    const jsons = await Promise.all(tasks.map(async(task) => {
      let json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'preprocessed.internal.json', true);
      if (!json) { // 没有 json 预处理的任务 / 数据
        json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.internal.json', true);
      }
      if (!json) { // 老数据需要兼容。直接读取json
        json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json', true);
      }
      return json;
    }));
    const json = this.combineJson(jsons);
    return json;
  }

  public async combineAiEditJsonByTaskIds(tasks: { taskId: number; appKey: string }[]) {
    const jsons = await Promise.all(tasks.map(async(task) => {
      const html = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.html', true);
      return htmlToJsonV5({ html });
    }));
    const json = this.combineJson(jsons);
    return json;
  }

  /**
   * 根据任务ID获取任务的json数据
   * @param task
   *
   */
  public async getJsonByTaskId(task: {taskId: number; appKey: string; }) {
    let json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'preprocessed.internal.json');
    if (!json) {
      // 没有 json 预处理的任务 / 数据
      json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.internal.json');
    }
    if (!json) {
      // 老数据需要兼容。直接读取json
      json = await this.service.task.base.getOssData(task.appKey, task.taskId, 'formatted.json');
    }
    return json;
  }

  public combineJson(jsons: any[][]) {
    let result: any[] | undefined;
    jsons.forEach((json: any[]) => {
      if (!result || !result.length) {
        result = json;
        return;
      }
      json.forEach((item) => {
        let siblings = result!;
        while (true) {
          const current = siblings[siblings.length - 1];
          if (!current) {
            break;
          }
          if (item.node_type === 'chapter' ?
            current.node_type !== 'chapter' || current.content.level >= item.content.level :
            current.node_type !== 'chapter') {
            break;
          }
          siblings = current.children;
        }
        siblings.push(item);
      });
    });
    result = result || [];
    for (const { node, level } of iterateNode(result)) {
      node.node_level = level + 1;
    }
    return result;
  }

  public async getRelateOne(options: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>> = {}) {
    const { service, app } = this;
    const { fn, col } = app.model;
    const book = await this.getOne(options) as (superSequelize.Attributes<Attributes> & {
      reviewUsername: string;
      startReviewTime: string;
      endReviewTime: string;
      imageCount: number;
      imagePreprocessed: number;
      subTask: any;
      jsonUrl: string;
      htmlUrl: string;
      docUrl: string;
      catalogUrl: string;
    } | null);
    if (!book) {
      return null;
    }
    book.imagePreprocessed = 0;
    book.imageCount = 0;
    const [subTask, images, users] = await Promise.all([
      this.getSubTaskStatuses(book.id || 0),
      service.image.getAll({
        where: { bookId: book.id, disabled: false },
        attributes: ['preprocessed', [fn('COUNT', col('id')), 'count']],
        group: ['preprocessed'],
      } as any),
      service.user.search([book.reviewUserId])
    ]);
    for (const { preprocessed, count } of images as any) {
      if (preprocessed) {
        book.imagePreprocessed += count;
      }
      book.imageCount += count;
    }
    book.subTask = subTask;
    book.reviewUsername = users.length ? users[0].nickname : '';
    book.htmlUrl = this.getUrl(book.appKey, book.id || 0, 'html', true);
    book.jsonUrl = this.getUrl(book.appKey, book.id || 0, 'json', true);
    book.docUrl = this.getUrl(book.appKey, book.id || 0, 'docx', true);
    book.catalogUrl = this.getUrl(book.appKey, book.id || 0, 'catalog.json', true);
    return book;
  }

  public async getSubTaskStatuses(bookId: number) {
    // 获取子任务状态时，需要筛选掉审核任务
    const { service, app } = this;
    const { fn, col } = app.model;
    const taskStatuses = service.task.base.statuses;
    const tasks = await service.task.base.getAll({
      where: {
        bookId,
        mergedTaskId: null,
      },
      attributes: ['status',[fn('COUNT', col('id')), 'count']],
      group: ['status'],
    }) as any;
    const statuses = {
      reviewed: 0,
      marked: 0,
      error: 0,
      count: 0,
    };
    for (const { status, count } of tasks) {
      statuses.count += count;
      if (status === taskStatuses.error) {
        statuses.error += count;
        statuses.marked += count;
      } else if (status === taskStatuses.reviewed) {
        statuses.reviewed += count;
        statuses.marked += count;
      } else if (status >= taskStatuses.unreviewed) {
        statuses.marked += count;
      }
    }
    return statuses;
  }

  public async getPreprocessingBooks() {
    const { service, app } = this;
    const { statuses } = service.task.base;
    const { fn, col } = app.model;
    const tasks = await service.task.base.getAll({
      where: { status: statuses.columnAutoProcessed, rerun: false },
      attributes: [[fn('DISTINCT', col('bookId')), 'bookId']],
    });
    return tasks.map((item) => item.bookId);
  }

  public async getCatalog(bookId: number) {
    const { service } = this;
    const { statuses } = service.task.base;
    const tasks = await service.task.base.getAll({
      where: {
        bookId ,
        mergedTaskId: { [Op.not]: null },
      },
      attributes: ['catalog', 'status', 'taskId', 'mergedTaskId'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    let bookCatalog = [];
    const alreadyProcessed: number[] = [];
    for (const { catalog, mergedTaskId } of tasks) {
      // 找到 mergedTaskId 不为空的任务
      if (mergedTaskId && !alreadyProcessed.includes(mergedTaskId)) {
        const mergedTask = await service.task.base.getOne({ where: { taskId: mergedTaskId } });
        alreadyProcessed.push(mergedTaskId);
        if (mergedTask && mergedTask.status !== statuses.reviewed) {
          return [];
        }
      }
      bookCatalog = bookCatalog.concat(catalog ? JSON.parse(catalog) : []);
    }
    return bookCatalog;
  }

}
