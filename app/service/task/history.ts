/**
 * @file task日志历史
 * <AUTHOR>
 */
'use strict';

import { Context } from 'egg';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, Instance } from '../../model/taskHistory';

const markTypes = {
  apply: { id: 11, name: '申领标注' },
  quit: { id: 12, name: '放弃标注' },
  save: { id: 13, name: '保存标注图片' },
  confirm: { id: 14, name: '提交标注' },
  returnBack: { id: 15, name: '标注异常打回' },
  revoke: { id: 16, name: '撤回标注' },
  quitOnChangeSubject: { id: 17, name: '修改学科后，回收标注任务' },
};

const reviewTypes = {
  apply: { id: 21, name: '申领审核' },
  quit: { id: 22, name: '放弃审核' },
  save: { id: 23, name: '保存审核图片' },
  confirm: { id: 24, name: '提交审核' },
  returnBack: { id: 25, name: '审核异常打回' },
  revoke: { id: 26, name: '撤回审核' },
  remark: { id: 27, name: '重新标注' },
};

const columnTypes = {
  apply: { id: 31, name: '申领划块' },
  quit: { id: 32, name: '放弃划块' },
  save: { id: 33, name: '保存划块图片' },
  // confirm:{ id:34, name:'提交划块' },
  returnBack: { id: 35, name: '划块异常打回' },
  revoke: { id: 36, name: '撤回划块' },
  restore: { id: 37, name: '重置划块' },
  restart: { id: 38, name: '重新划块识别' },
};

const taskV2Types = {
  wordCreate: { id: 201, name: 'word任务创建' },
  pdfCreate: { id: 222, name: 'pdf任务创建' },
  fbdCreate: { id: 202, name: 'fbd任务创建' },
  subWordCreate: { id: 203, name: 'word子任务创建' },
  subFbdCreate: { id: 204, name: 'fbd子任务创建' },
  markSave: { id: 205, name: '保存标注' },
  markConfirm: { id: 206, name: '提交标注' },
  reviewSave: { id: 207, name: '保存审核' },
  reviewConfirm: { id: 208, name: '提交审核' },
  reviewChangeChapter: { id: 209, name: '修改审核JSON' },
  refreshChapter: { id: 209, name: '机器刷新HTML标题' },
  reviewRegenerate: { id: 210, name: '重新生成JSON' },
  restart: { id: 211, name: '重跑任务' },
  wordUpdate: { id: 212, name: '更新word结果' },
  fbdUpdate: { id: 213, name: '更新fbd结果' },
  splitApply: { id: 214, name: '申领分割文件' },
  splitSave: { id: 215, name: '保存分割文件' },
  splitQuit: { id: 216, name: '放弃分割文件' },
  splitReduct: { id: 217, name: '还原分割文件' },
  splitJump: { id: 218, name: '跳过分割文件' },
  splitRevoke: { id: 219, name: '重新分割文件' },
  splitConfirm: { id: 220, name: '提交分割文件' },
  splitReConfirm: { id: 221, name: '重新提交分割文件' },
};

const preCropTypes = {
  submit: { id: 51, name: '提交预切图结果' },
  finish: { id: 52, name: '完成预切图' },
  restart: { id: 53, name: '重新预切图' },
  check: { id: 54, name: '预切图检查' },
  crop: { id: 55, name: '预切图' },
};

const otherTypes = {
  restart: { id: 101, name: '重跑' },
  del: { id: 102, name: '删除' },
  rerunImage: { id: 103, name: '重跑图片' },
  setTaskType: { id: 104, name: '更新任务类型' }, // 未设置、限时、生产计划
  returnBack: { id: 105, name: '标注异常打回' },
  revoked: { id: 106, name: '撤销任务' },
  changeSubject: { id: 107, name: '修改学科/学段' },
  autoRestart: { id: 108, name: '超时自动重跑' },
};

const types = [
  { groupType: 'mark', items: markTypes },
  { groupType: 'review', items: reviewTypes },
  { groupType: 'column', items: columnTypes },
  { groupType: 'preCrop', items: preCropTypes },
  { groupType: 'other', items: otherTypes },
  { groupType: 'taskV2', items: taskV2Types }
];

const operationMap: { [key: string]: { groupType: string; type: string; id: number; name: string } } = {};
types.forEach(({ groupType, items }) => {
  Object.entries(items).forEach(([type, item]) => {
    operationMap[item.id] = { ...item, groupType, type };
  });
});

export default class TaskHistoryService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.TaskHistory, defineAttributes);
  }

  public markTypes = markTypes;

  public reviewTypes = reviewTypes;

  public columnTypes = columnTypes;

  public preCropTypes = preCropTypes;

  public taskV2Types = taskV2Types;

  public otherTypes = otherTypes;

  private displayTimeCost(costTime?: number) {
    if (!costTime) return;
    const seconds = Math.round(costTime / 1000);
    const sec = seconds % 60;
    const minutes = (seconds - sec) / 60;
    const min = minutes % 60;
    const hours = (minutes - min) / 60;
    return `${hours ? `${hours}小时` : ''}${min || hours ? `${min}分` : ''}${sec}秒`;
  }

  public async getDetailByTaskId(taskId: number) {
    const { service } = this;
    const histories = await super.getAll({
      where: { taskId },
      attributes: ['userId', 'createTime', 'taskId', 'type', 'costTime', 'data'],
      order: [['id', 'ASC']],
    }) as any[];
    if (!histories.length) {
      return [];
    }
    const userMap = {};
    const userIds = histories.map((item) => item.userId);
    // 搜索用户名
    if (userIds.length) {
      const users = await service.user.search(userIds);
      for (const { userId, nickname } of users) {
        userMap[userId] = nickname;
      }
    }
    return histories.map((item) => {
      item.username = userMap[item.userId] || '';
      item.costTimeStr = this.displayTimeCost(item.costTime) || '';
      item.createTime = Number(new Date(item.createTime));
      item.operation = operationMap[item.type];
      delete item.type;
      return item;
    });
  }

}
