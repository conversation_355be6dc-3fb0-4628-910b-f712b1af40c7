/**
 * @file 任务配置表
 */

'use strict';
import { Application } from 'egg';
import * as Sequelize from 'sequelize';
import ModelBizAttributes from '../core/base/modelBizAttributes';
import superSequelize from '../../typings/app/core/modelService';
import { IMetaAttrs, metaAttributes } from '../core/base/metaService';
import { IProjectMetas } from './projectMeta';

export interface IPdfInfo {
  bodyPageStart: string;
  bodyPageEnd: string;
  answerPageStart: string;
  answerPageEnd: string;
  bodyPdf: string;
  answerPdf: string;
  isConfirm?: boolean;
}

export interface IPageCountInfo {
  name: string;
  mode: number,
  md5: string,
  internalPages: number,
  actualPages: number,
  pageColumns: number,
  pageSize: number,
}
export type ITaskMetas = (IProjectMetas & {
  bookName: string;
  pdfInfo: IPdfInfo;
  splitPdfInfo: IPdfInfo;
  confirmPdfInfo: IPdfInfo;
  path?: string;
  runAiFixed: boolean
}) ;

export const TASK_DEFAULT_METAS: { [key in keyof ITaskMetas]?: string } = {};

type CusAttributes = IMetaAttrs<'taskId', number, ITaskMetas>;

// 字段声明
export type Attributes = superSequelize.Attributes<CusAttributes>;

// 实例类声明
export type Instance = superSequelize.Instance<CusAttributes>;

// 实例类声明
export const defineAttributes: any = {
  ...ModelBizAttributes,
  ...metaAttributes,
  taskId: {
    type: Sequelize.BIGINT(16),
    allowNull: false,
    comment: '任务id',
    defaultValue: 0,
    allowUpdate: false,
  },
};

export default (app: Application) => {
  const { model } = app;
  return model.define<Instance, Attributes>('task_meta', defineAttributes, {
    freezeTableName: true,
    indexes: [
      {
        // 所有unique: true的索引必须要引入isDel
        unique: true,
        fields: ['taskId', 'key', 'isDel'],
      },
      {
        unique: false,
        fields: ['key'],
      }
    ],
  });
};
