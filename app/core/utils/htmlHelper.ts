import { parse, parseDefaults } from 'himalaya';
import { escape, unescape } from 'he';
import { strapText } from './helper';
import { iterateNode } from './treeHelper';

const cheerio = require('cheerio');

export { escape } from 'he';
export const SELF_CLOSE_TAGS: string[] = parseDefaults.voidTags;

function decode(str: string, options: { isAttributeValue?: boolean; strict?: boolean; nbspToSp?: boolean } = {}) {
  const text: string = unescape(str, options);
  const { nbspToSp = true } = options;
  return nbspToSp ? text.replace(/\u00a0/g, ' ') : text;
}

export { decode as unescape };

export interface IHtmlNodeProperties {
  dataset: { [key: string]: string | undefined };
  attrs: { [key: string]: string | undefined };
  cls: { [key: string]: true | undefined };
  style?: { [key: string]: string | undefined };
}

// 纯文本节点
export interface ITextNode {
  type: 'text';
  tagName?: never;
  content: string;
}

// 注释节点
export interface ICommentNode {
  type: 'comment';
  tagName?: never;
  content: string;
}

// 相当于innerHtml，只是为了方便拼接html
export interface ISourceHtmlNode {
  type: 'html';
  tagName?: never;
  content: string;
}

// 元素节点
export interface IElementNode extends IHtmlNodeProperties {
  type: 'element';
  tagName: string;
  children: THtmlNode[];
}

// 节点片段
export interface IFragmentNode {
  type: 'fragment';
  tagName?: never;
  children: THtmlNode[];
}

export type THtmlNode = ITextNode | ICommentNode | ISourceHtmlNode | IElementNode | IFragmentNode;

export function parseHtml(html: string, { formatStyleDef = false } = {}): THtmlNode[] {
  const nodeTree = parse(html);
  formatNodesAttrs(nodeTree, { formatStyleDef });
  return nodeTree;
}

// 将所有节点的 attributes 转换为 attrs+dataset
function formatNodesAttrs(nodes: any[], { formatStyleDef = false }) {
  for (const { node } of iterateNode(nodes)) {
    if (node.type !== 'element') {
      continue;
    }
    const { attrs, dataset, cls, style } = formatAttrs(node, { formatStyleDef });
    delete node.attributes;
    node.attrs = attrs;
    node.dataset = dataset;
    node.cls = cls;
    node.style = style;
  }
}

/*
 * 将 attributes 转换为 attrs+dataset
 * [{key: 'data-label', value: 'test'}, {'key': 'class', value: 'align-right'}]  ==>
 * attrs: {'data-label': 'test', class: 'align-right'},  dataset: {label: 'test'}
 */
function formatAttrs({ attributes }, { formatStyleDef = false }): IHtmlNodeProperties {
  const attrs = {};
  const dataset = {};
  const cls = {};
  const style = {};
  const input = attributes || [];
  input.forEach((attr) => {
    // 标准html <span disabled> 中 disabled 的属性值为 true
    const value = attr.value == null ? 'true' : unescape(attr.value);
    if (attr.key.startsWith('data-')) {
      dataset[attr.key.substring('data-'.length)] = value;
    }
    attrs[attr.key] = value;
  });
  if (attrs['class']) {
    attrs['class'].trim().split(/\s+/).forEach((c) => {
      cls[c] = true;
    });
  }
  if (formatStyleDef && attrs['style']) {
    attrs['style'].split(';').map((s) => s.trim()).filter((s) => s).forEach((s) => {
      const [k, v] = s.split(':').map((s) => s.trim());
      style[k] = v;
    });
  }
  return { attrs, dataset, cls, style };
}

function stringifyAttrs({ attrs }) {
  const result: string[] = [];
  if (attrs) {
    Object.keys(attrs).forEach((key) => {
      let value = attrs[key];
      if (key === 'class') {
        value = value.replace(/\s+/, ' ').trim();
        if (!value) {
          return;
        }
      }
      if (value === 'true') {
        result.push(key);
      } else {
        value = escape(`${value}`);
        result.push(`${key}="${value}"`);
      }
    });
  }
  return result.length ? ` ${result.join(' ')}` : '';
}

type TextOpts = { wrapLatexWith?: string, strip?: boolean };

// 将dom转换为纯文本
export function getText(nodes: THtmlNode[], { wrapLatexWith, strip = false }: TextOpts = {}) {
  const result: string[] = [];
  for (const { node, index, parent } of iterateNode(nodes, { order: 'post' })) {
    if (node.type === 'element' && node.tagName === 'p') {
      if (index !== nodes.length - 1) {
        result.push('\n');
      }
    } else if (parent &&
      parent.type === 'element' &&
      parent.dataset.label === 'latex' &&
      wrapLatexWith != null) {
      if (parent.dataset.value && parent.dataset.value.trim()) {
        result.push(`${wrapLatexWith}${formatLatex(parent.dataset.value)}${wrapLatexWith}`);
      }
    } else if (node.type === 'element' && node.dataset['image-text']) {
      result.push(node.dataset['image-text']);
    } else if (node.type === 'text') {
      let content = strip ? strapText(node.content, false) : node.content;
      content = unescape(content);
      result.push(content);
    }
    // type=comment/html 将被忽略
  }
  return result.join('');
}

type HtmlOpts = {
  strip?: boolean,
  reserveComment?: boolean,
  xmlMode?: boolean,
  wrapLatexWith?: string,
};

// 将dom转换为html
export function getHtml(nodes: THtmlNode[], {
  strip = false,
  reserveComment = true,
  xmlMode = false,
  wrapLatexWith,
}: HtmlOpts = {}): string {
  const result = nodes.map((node) => {
    if (node.type === 'comment') {
      if (!reserveComment) {
        return '';
      }
      return `<!--${strip ? strapText(node.content, false) : node.content}-->`;
    }
    if (node.type === 'text') {
      return strip ? strapText(node.content, false) : node.content;
    }
    if (node.type === 'fragment') {
      return getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    }
    if (node.type === 'html') {
      return node.content;
    }
    if ((node as any).type !== 'element') return ''; // 不清楚为什么有其他类型
    if (SELF_CLOSE_TAGS.includes(node.tagName) || xmlMode && !node.children.length) {
      return `<${node.tagName}${stringifyAttrs(node)}/>`;
    }
    if (node.dataset.label === 'latex' && wrapLatexWith != null) {
      if (!node.dataset.value || !node.dataset.value.trim()) return '';
      return `${wrapLatexWith}${formatLatex(node.dataset.value)}${wrapLatexWith}`;
    }
    const content = getHtml(node.children, { strip, reserveComment, xmlMode, wrapLatexWith });
    return `<${node.tagName}${stringifyAttrs(node)}>${content}</${node.tagName}>`;
  });
  return result.join('');
}

function formatLatex(latex) {
  if (!latex) return '';
  return latex.replace(/</g, '\\lt ').replace(/>/g, '\\gt ');
}

export function addTextForNode(nodes: THtmlNode[], text: string, mode: 'prepend' | 'append' = 'prepend') {
  // html node 头部或者尾部插入 inline 文本
  const textNode: THtmlNode = { type: 'text', content: text };
  if (!nodes.length) { // 如果为空，直接插入文本节点
    nodes.push(textNode);
    return nodes;
  }
  for (const { node, siblings } of iterateNode(nodes, { back: mode === 'append' })) {
    if (node.type === 'element' && node.tagName !== 'p' || ['text', 'html'].includes(node.type)) {
      // 找到第一个(最后一个)block节点，在其头(尾)部添加文本节点
      if (mode === 'prepend') {
        siblings.unshift(textNode);
      } else {
        siblings.push(textNode);
      }
      break;
    }
  }
  return nodes;
}

export function addTextForHtml(html: string, text: string, mode: 'prepend' | 'append' = 'prepend') {
  const nodes = parseHtml(html);
  addTextForNode(nodes, text, mode);
  const result = getHtml(nodes);
  return result;
}

export function getNodeSize({ attrs }: IElementNode, prop: 'width' | 'height') {
  // 获取节点样式中的宽度、高度属性。（如表格宽度、单元格宽度）
  let length = attrs[prop];
  if (!length) {
    if (!attrs.style) return;
    const styles: any = {};
    attrs.style.split(';').map((s) => s.trim()).filter((s) => s).forEach((item) => {
      const [key, value] = item.split(':').map((s) => s.trim());
      if (!key || !value) return;
      styles[key] = value;
    });
    length = styles[prop];
  }
  length = length && length.trim();
  if (!length) return;
  const match = length.match(/(\d*\.?\d*)(px|%)?/);
  if (!match || !match[1]) return;
  const size = Number(match[1]);
  const type = (match[2] || 'px') as 'px' | '%';
  return { type, size };
}

export function flushHTML(html: string): string {
  // 使用cheerio加载HTML，设置不解析为完整HTML文档
  const $ = cheerio.load(html, {
    decodeEntities: false,
    xmlMode: true,
  });

  // 处理math标记 - 首先处理最内层的标记
  function processMath(): void {
    $('*').each(function(this: any) {
      const $element = $(this);
      const content = $element.html();
      if (!content || !content.includes('😊math')) return;

      // 匹配所有math标记
      const mathMatches = [...content.matchAll(/😊(math)(\d+)😊([\s\S]*?)😊\1\2😊/g)];

      if (mathMatches.length > 0) {
        let newContent = content;

        for (const match of mathMatches) {
          const symbol = match[1] + match[2];
          const innerContent = match[3];

          // 创建带有symbol属性的span元素
          const spanHtml = `<span symbol="${symbol}">$${innerContent}$</span>`;

          // 替换原始内容
          newContent = newContent.replace(match[0], spanHtml);
        }

        if (newContent !== content) {
          $element.html(newContent);
        }
      }
    });
  }

  // 处理img标记
  function processImg(): void {
    $('*').each(function(this: any) {
      const $element = $(this);
      const content = $element.html();
      if (!content || !content.includes('😊img')) return;

      // 匹配所有img标记
      const imgMatches = [...content.matchAll(/😊(img)(\d+)😊([\s\S]*?)😊\1\2😊/g)];

      if (imgMatches.length > 0) {
        let newContent = content;

        for (const match of imgMatches) {
          const symbol = match[1] + match[2];
          const innerContent = match[3];

          // 查找内容中的img标签
          const imgRegex = /<img[^>]*?/;
          const imgMatch = innerContent.match(imgRegex);

          if (imgMatch) {
            // 创建临时元素以便于操作
            const modifiedImg = innerContent.replace(imgRegex, `<img symbol="${symbol}" `);

            // 替换原始内容
            newContent = newContent.replace(match[0], modifiedImg);
          }
        }

        if (newContent !== content) {
          $element.html(newContent);
        }
      }
    });
  }

  /**
 * processParaRevisedCheerio 函数
 *
 * 该函数用于处理HTML内容中包含特殊标记 😊paraXX😊 的段落。
 * 它会识别这些标记，将标记之间的内容（即使跨越多个段落）提取出来，
 * 并将这些内容合并到标记对的起始段落中，同时移除其他参与的段落。
 * 此版本特别适配了 Cheerio 库。
 *
 * @param {CheerioAPI} $ - Cheerio 的实例，通常通过 const $ = cheerio.load(htmlString) 获取。
 * @returns {void} 该函数直接修改传入的 Cheerio 对象所代表的 DOM 结构。
 * 如果需要获取修改后的 HTML 字符串，可以在函数外部调用 $.html()。
 */
  function processParaRevised() {
    // 获取所有 <p> 元素，并将它们转换为 Cheerio 对象数组
    // 注意：在 Cheerio 中，通常你已经有一个 $ 对象代表整个文档。
    // 如果你的 $allParas 是直接从 $('p') 获得的 Cheerio 集合，
    // 那么在遍历时，$(this) 或直接使用集合中的元素即可。
    // 为了与之前版本保持一致性并明确每个元素是 Cheerio 对象，我们进行 map。
    const $allParas = $('p').toArray().map((p) => $(p));

    // 步骤 0: 为所有段落保存其原始内容的副本，用于后续准确的索引和内容提取
    // 在 Cheerio 中，我们使用 attr 来设置一个临时的 data-* 属性
    $allParas.forEach(($p) => {
      $p.attr('data-temp-original-html', $p.html());
    });

    // 步骤 1: 查找文档中所有的 para 标记，并按其符号 (symbol) 分组
    const markersBySymbol = {}; // 用于存储按符号分类的标记信息
    $allParas.forEach(($p, pIndex) => {
      // 从保存的原始内容中查找，确保索引准确性
      const html = $p.attr('data-temp-original-html') || '';
      const markerRegex = /😊(para\d+)😊/g; // 正则表达式匹配如 😊para21😊
      let match;

      // 遍历段落中所有匹配的标记
      while ((match = markerRegex.exec(html)) !== null) {
        const symbol = match[1]; // 提取标记的符号，例如 "para21"
        if (!markersBySymbol[symbol]) {
          markersBySymbol[symbol] = []; // 如果是新的符号，初始化数组
        }
        // 记录标记信息：
        // - pIndex: 标记所在原始段落的索引
        // - startIndexInP: 标记在段落HTML中的起始位置
        // - endIndexInP: 标记在段落HTML中的结束位置
        markersBySymbol[symbol].push({
          pIndex,
          startIndexInP: match.index,
          endIndexInP: match.index + match[0].length,
        });
      }
    });

    // 步骤 2: 为每个符号，将其标记配对形成“块”，并提取内容
    const blocks: any[] = []; // 用于存储所有形成的逻辑块的信息

    // 按符号名称排序，以确保处理顺序的确定性，这在处理冲突时可能有用
    const sortedSymbols = Object.keys(markersBySymbol).sort();

    for (const symbol of sortedSymbols) {
      const symbolMarkers = markersBySymbol[symbol]; // 获取当前符号的所有标记

      // 将标记两两配对（一个开始标记，一个结束标记）
      for (let i = 0; i < symbolMarkers.length; i += 2) {
        const openMarker = symbolMarkers[i]; // 当前对的开始标记
        const closeMarker = (i + 1 < symbolMarkers.length) ? symbolMarkers[i + 1] : null; // 当前对的结束标记

        if (closeMarker) { // 如果找到了一个完整的标记对
          let blockContent = ''; // 用于存储这个块提取到的内容
          const involvedPs_pIndexes_in_block = new Set(); // 记录此块涉及到的所有原始段落的索引

          if (openMarker.pIndex === closeMarker.pIndex) {
            // 情况 A: 开始和结束标记在同一个段落内
            const originalHtml = $allParas[openMarker.pIndex].attr('data-temp-original-html');
            blockContent = originalHtml.substring(openMarker.endIndexInP, closeMarker.startIndexInP);
            involvedPs_pIndexes_in_block.add(openMarker.pIndex);
          } else {
            // 情况 B: 开始和结束标记跨越多个段落
            // 1. 从开始段落提取内容 (标记之后的部分)
            const firstPOriginalHtml = $allParas[openMarker.pIndex].attr('data-temp-original-html');
            blockContent += firstPOriginalHtml.substring(openMarker.endIndexInP);
            involvedPs_pIndexes_in_block.add(openMarker.pIndex);

            // 2. 提取所有中间段落的完整内容
            for (let pIdx = openMarker.pIndex + 1; pIdx < closeMarker.pIndex; pIdx++) {
              const intermediatePOriginalHtml = $allParas[pIdx].attr('data-temp-original-html');
              blockContent += intermediatePOriginalHtml;
              involvedPs_pIndexes_in_block.add(pIdx);
            }

            // 3. 从结束段落提取内容 (标记之前的部分)
            const lastPOriginalHtml = $allParas[closeMarker.pIndex].attr('data-temp-original-html');
            blockContent += lastPOriginalHtml.substring(0, closeMarker.startIndexInP);
            involvedPs_pIndexes_in_block.add(closeMarker.pIndex);
          }
          blocks.push({
            symbol: symbol,
            content: blockContent.trim(), // 去除内容首尾的空白字符
            targetP_pIndex: openMarker.pIndex, // 将块的起始段落索引用作目标段落的索引
            involvedPs_pIndexes: Array.from(involvedPs_pIndexes_in_block), // 转换为数组
          });
        } else { // 未能配对的孤立开始标记 (奇数个标记的情况)
          blocks.push({
            symbol: symbol,
            content: '', // 孤立标记默认内容为空
            targetP_pIndex: openMarker.pIndex,
            involvedPs_pIndexes: [openMarker.pIndex], // 只涉及其自身所在的段落
          });
        }
      }
    }

    // 步骤 3: 根据提取的块信息，更新DOM
    // 创建一个数组来记录每个原始段落的最终状态:
    // - null: 保持原样 (未参与任何块)
    // - 'remove': 此段落将被移除
    // - object { content, symbol }: 此段落将成为目标段落，并更新其内容和 symbol 属性
    const finalParagraphStates = new Array($allParas.length).fill(null);

    blocks.forEach((block) => {
      const currentTargetState = finalParagraphStates[block.targetP_pIndex];

      // 设置目标段落的状态。
      // 注意：如果一个段落是多个不同符号块的起点，此处的简单逻辑是后处理的块会覆盖先处理的块。
      // 对于更复杂的嵌套或重叠情况，可能需要更高级的冲突解决策略。
      if (!currentTargetState || typeof currentTargetState === 'string' || !currentTargetState.symbol) {
        finalParagraphStates[block.targetP_pIndex] = {
          content: block.content || '&nbsp;', // 如果内容为空，则使用 &nbsp;
          symbol: block.symbol,
        };
      } else {
        // 可以选择性地处理冲突，例如打印警告
        // console.warn(`段落索引 ${block.targetP_pIndex} 作为目标时发生冲突: 已有符号 ${currentTargetState.symbol}, 新符号 ${block.symbol}`);
      }

      // 标记此块中其他涉及的段落（非目标段落）为“待移除”，
      // 前提是它们没有成为其他块的目标段落。
      block.involvedPs_pIndexes.forEach((pIndex) => {
        if (pIndex !== block.targetP_pIndex) { // 如果不是此块的目标段落
          const involvedPState = finalParagraphStates[pIndex];
          // 仅当该段落未被指定为其他块的目标时，才将其标记为移除
          if (!involvedPState || typeof involvedPState === 'string' || !involvedPState.symbol) {
            finalParagraphStates[pIndex] = 'remove';
          }
        }
      });
    });

    // 从后向前遍历原始段落列表，以安全地应用更改 (特别是删除操作，避免影响后续索引)
    for (let i = $allParas.length - 1; i >= 0; i--) {
      const state = finalParagraphStates[i];
      const $p = $allParas[i]; // $p 是一个 Cheerio 对象

      if (state === 'remove') {
        $p.remove(); // 移除段落
      } else if (state && typeof state === 'object' && state.symbol) { // 如果是目标段落
        $p.html(state.content); // 设置新的HTML内容
        $p.attr('symbol', state.symbol); // 设置 symbol 属性
        // $p.removeAttr('data-para-symbol'); // 如果之前有使用 data-para-symbol，可以移除
      }
      // else: 如果状态为 null, 表示该段落未参与任何块处理，保持原样

      // 清理临时存储的原始HTML数据属性
      $p.removeAttr('data-temp-original-html');
    }

    // 函数执行完毕后，Cheerio 的 $ 对象所代表的 DOM 结构已经被修改。
    // 如果需要获取修改后的 HTML 字符串，可以在函数外部调用 $.html()。
    // 例如:
    // const cheerio = require('cheerio');
    // const myHtml = "<p>😊para1😊Content😊para1😊</p>";
    // const $ = cheerio.load(myHtml);
    // processParaRevisedCheerio($);
    // console.log($.html()); // 输出: <p symbol="para1">Content</p>
  }

  // 处理para标记
  // @ts-ignore
  function processPara(): void {
    // 第一步：预处理段落中的多个不同标记
    $('p').each(function(this: any) {
      const $p = $(this);
      const content = $p.html() || '';
      if (!content || !content.includes('😊para')) return;

      // 检查是否包含多个不同的para标记
      const allParaMatches = [...content.matchAll(/😊(para)(\d+)😊/g)];
      const uniqueParaSymbols = [...new Set(allParaMatches.map((m) => m[1] + m[2]))];

      // 如果只有一个标记或没有标记，不需要额外处理
      if (uniqueParaSymbols.length <= 1) return;

      // 处理多个不同标记情况：将段落分割成多个段落
      let currentContent = content;
      const $reference = $p;

      // 从最后一个标记开始处理，避免位置变化影响
      for (let i = uniqueParaSymbols.length - 1; i >= 0; i--) {
        const symbol = uniqueParaSymbols[i];
        const symbolRegex = new RegExp(`😊${symbol}😊([\\s\\S]*?)😊${symbol}😊|😊${symbol}😊`, 'g');
        const symbolMatches = [...currentContent.matchAll(symbolRegex)];

        if (symbolMatches.length === 0) continue;

        // 提取此标记包围的内容或标记后的内容
        let extractedContent = '';
        let startPos = -1;
        let endPos = -1;

        // 完整的标记对情况
        if (symbolMatches[0][0].includes(`😊${symbol}😊`) && symbolMatches[0][0].includes(`😊${symbol}😊`)) {
          extractedContent = symbolMatches[0][1] || '';
          startPos = currentContent.indexOf(symbolMatches[0][0]);
          endPos = startPos + symbolMatches[0][0].length;
        }
        // 单独标记情况 - 提取标记后的所有内容
        else {
          startPos = currentContent.indexOf(symbolMatches[0][0]);
          endPos = currentContent.length;
          extractedContent = currentContent.substring(startPos + symbolMatches[0][0].length);
        }

        if (startPos >= 0 && extractedContent) {
          // 创建新段落包含提取的内容，并标记
          const $newP = $('<p></p>');
          $newP.html(extractedContent);
          $newP.attr('data-para-symbol', symbol);

          // 插入到原段落后面
          $reference.after($newP);

          // 从原内容中移除已处理的部分
          currentContent = currentContent.substring(0, startPos) +
                          (endPos < currentContent.length ? currentContent.substring(endPos) : '');
        }
      }

      // 更新原段落，仅保留剩余内容
      if (currentContent.trim()) {
        $p.html(currentContent || '&nbsp;');
      } else {
        $p.remove();
      }
    });

    // 第二步：收集所有para标记和内容
    const paraMap: Record<string, string[]> = {};
    const paraElementMap: Record<string, any[]> = {};

    // 收集所有段落和它们的para标记
    $('p').each(function(this: any) {
      const $p = $(this);
      const content = $p.html();
      if (!content) return;

      // 查找完整的标记对 😊paraXX😊内容😊paraXX😊
      const fullParaMatch = content.match(/😊(para)(\d+)😊([\s\S]*?)😊\1\2😊/);
      if (fullParaMatch) {
        const symbol = fullParaMatch[1] + fullParaMatch[2];
        const innerContent = fullParaMatch[3];

        if (!paraMap[symbol]) {
          paraMap[symbol] = [];
          paraElementMap[symbol] = [];
        }
        paraMap[symbol].push(innerContent);
        paraElementMap[symbol].push({ element: $p, hasContent: Boolean(innerContent.trim()) });

        // 标记这个段落，但暂时不修改内容
        $p.attr('data-para-symbol', symbol);
        return;
      }

      // 查找单独的标记 😊paraXX😊
      const singleParaMatch = content.match(/😊(para)(\d+)😊/);
      if (singleParaMatch) {
        const symbol = singleParaMatch[1] + singleParaMatch[2];

        // 提取标记前后的内容，保留原始内容
        let cleanContent = '';

        // 如果是开始标记，获取标记后的内容
        if (content.startsWith(`😊${symbol}😊`)) {
          cleanContent = content.substring((`😊${symbol}😊`).length);
        }
        // 如果是结束标记，获取标记前的内容
        else if (content.endsWith(`😊${symbol}😊`)) {
          cleanContent = content.substring(0, content.length - (`😊${symbol}😊`).length);
        }
        // 如果标记在中间，保留所有内容
        else {
          cleanContent = content.replace(new RegExp(`😊${symbol}😊`, 'g'), '');
        }

        // 如果清理后没有内容，使用&nbsp;
        if (!cleanContent.trim()) {
          cleanContent = '&nbsp;';
        }

        const hasContent = Boolean(cleanContent.trim() && cleanContent !== '&nbsp;');

        if (!paraMap[symbol]) {
          paraMap[symbol] = [];
          paraElementMap[symbol] = [];
        }

        // 添加内容到映射
        paraMap[symbol].push(cleanContent);
        paraElementMap[symbol].push({ element: $p, hasContent });

        // 标记这个段落，但暂时不修改内容
        $p.attr('data-para-symbol', symbol);
      }
    });

    // 第三步：处理收集到的内容并应用到优先选择的段落，删除其他段落
    Object.keys(paraMap).forEach((symbol) => {
      const contents = paraMap[symbol];
      if (contents.length === 0) return;

      const elements = paraElementMap[symbol];
      if (!elements || elements.length === 0) return;

      // 优先选择有内容的段落，如果都没有内容则选择第一个
      let targetElement = elements[0].element;
      for (const item of elements) {
        if (item.hasContent) {
          targetElement = item.element;
          break;
        }
      }

      // 合并所有内容，保留原始HTML结构
      const mergedContent = contents.join(' ');

      // 修复：确保内容被保留，如果没有内容则使用&nbsp;
      if (mergedContent.trim()) {
        targetElement.html(mergedContent);
      } else {
        targetElement.html('&nbsp;');
      }

      // 保留内容的同时设置symbol属性
      targetElement.attr('symbol', symbol);
      targetElement.removeAttr('data-para-symbol');

      // 删除其他所有有相同标记的段落（不包括目标段落）
      elements.forEach((item) => {
        if (item.element !== targetElement) {
          item.element.remove();
        }
      });
    });
  }

  // 处理table标记
  function processTable(): void {
    $('p').each(function(this: any) {
      const $p = $(this);
      const content = $p.html();
      if (!content || !content.includes('😊tbl')) return;

      const tblMatches = [...content.matchAll(/😊(tbl)(\d+)😊/g)];
      if (tblMatches.length === 0) return;

      for (const match of tblMatches) {
        const symbol = match[1] + match[2];
        const tblNum = match[2];

        // 查找下一个table和包含同一个tbl标记的下一个p元素
        const $nextTable = $p.next('table');

        if ($nextTable.length > 0) {
          const $nextP = $nextTable.next('p');
          const nextPContent = $nextP.html() || '';

          if (nextPContent.includes(`😊tbl${tblNum}😊`)) {
            // 添加symbol属性到table
            $nextTable.attr('symbol', symbol);

            // 替换p元素中的tbl标记为&nbsp;
            $p.html('&nbsp;');
            $nextP.html('&nbsp;');
          }
        }
      }
    });
  }

  // 清理尚未处理的标记
  function cleanupRemainingMarkers(): void {
    // 再次处理未完成的img标记
    $('img').each(function(this: any) {
      const $img = $(this);
      if (!$img.attr('symbol')) {
        const parentHtml = $img.parent().html();

        if (parentHtml && parentHtml.includes('😊img')) {
          const imgMatch = parentHtml.match(/😊(img)(\d+)😊/);
          if (imgMatch) {
            $img.attr('symbol', imgMatch[1] + imgMatch[2]);
          }
        }
      }
    });

    // 处理特殊的公式格式（两个$符号问题）
    $('*').each(function(this: any) {
      const $el = $(this);
      const content = $el.html();
      if (!content) return;
    });

    // 再次处理未完成的math标记
    $('*').each(function(this: any) {
      const $el = $(this);
      let content = $el.html();
      if (!content) return;

      // 处理未完成的math标记
      if (content.includes('😊math')) {
        const mathMatches = content.match(/😊(math)(\d+)😊([\s\S]*?)😊\1\2😊/g);
        if (mathMatches) {
          for (const mathStr of mathMatches) {
            const match = mathStr.match(/😊(math)(\d+)😊([\s\S]*?)😊\1\2😊/);
            if (match) {
              const symbol = match[1] + match[2];
              const innerContent = match[3];
              const spanHtml = `<span symbol="${symbol}">$${innerContent}$</span>`;
              content = content.replace(mathStr, spanHtml);
            }
          }
          $el.html(content);
        }
      }

      // 处理未完成的para标记
      if (content.includes('😊para')) {
        const paraMatches = content.match(/😊(para)(\d+)😊([\s\S]*?)😊\1\2😊/g);
        if (paraMatches) {
          for (const paraStr of paraMatches) {
            const match = paraStr.match(/😊(para)(\d+)😊([\s\S]*?)😊\1\2😊/);
            if (match) {
              const symbol = match[1] + match[2];
              const innerContent = match[3];
              // 替换标记但保留内容
              content = content.replace(paraStr, innerContent);
              $el.html(content);
              // 设置symbol属性但不删除内容
              $el.attr('symbol', symbol);
            }
          }
        }
      }
    });

    // 清除所有剩余的😊xxx😊标记
    $('*').each(function(this: any) {
      const $el = $(this);
      let content = $el.html();
      if (!content) return;

      if (content.includes('😊')) {
        // 简单替换掉任何剩余的标记，替换成 &nbsp; 而不是直接删除
        content = content.replace(/😊(para|math|tbl|img)\d+😊/g, '&nbsp;');
        $el.html(content);
      }
    });

    // 确保所有段落都有内容，防止自闭合标签
    $('p').each(function(this: any) {
      const $p = $(this);
      const content = $p.html();

      // 如果段落没有内容或只有空白字符，添加&nbsp;
      if (!content || !content.trim()) {
        $p.html('&nbsp;');
      }
    });
  }

  // 处理所有标记类型
  function processAll(): void {
    // 处理顺序很重要，先处理嵌套的标记（math和img），再处理外层标记（para和tbl）
    processMath();
    processImg();
    processTable();
    // processPara();
    processParaRevised();
    // 清理剩余的标记
    cleanupRemainingMarkers();
  }

  // 执行处理
  processAll();

  // 保存修改后的HTML，但不包含html、head和body标签
  let modifiedHtml: string;
  if ($('body').length > 0) {
    // 如果存在body标签，只获取其内部内容
    modifiedHtml = $('body').html() || '';
  } else {
    // 否则获取根节点的内容
    modifiedHtml = $.html();
  }

  // 如果仍有html、body标签，手动去除
  modifiedHtml = modifiedHtml
    .replace(/<html[^>]*>/i, '')
    .replace(/<\/html>/i, '')
    .replace(/<body[^>]*>/i, '')
    .replace(/<\/body>/i, '')
    .replace(/<head>(.*?)<\/head>/i, '');

  // 修复自闭合的p标签，将<p/>替换为<p>&nbsp;</p>
  modifiedHtml = modifiedHtml.replace(/<p([^>]*?)\/>/g, '<p$1>&nbsp;</p>');

  // 确保所有带symbol属性的空段落都有内容，但不替换有内容的段落
  // 修复：只替换真正空的段落，不替换有内容的段落（即使内容周围有空白字符）
  modifiedHtml = modifiedHtml.replace(/<p([^>]*?)symbol="([^"]*?)"([^>]*?)>(\s*)<\/p>/g,
    (_match, p1, p2, p3, p4) => {
    // 如果内容只有空白字符，则替换为&nbsp;
      if (!p4 || !p4.trim()) {
        return `<p${p1}symbol="${p2}"${p3}>&nbsp;</p>`;
      }
      // 否则保留原内容
      return _match;
    });

  return modifiedHtml;
}
