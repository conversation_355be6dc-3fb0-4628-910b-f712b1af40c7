/**
 * @file 开放路由
 * <AUTHOR>
 * @desc 原则上开放接口只提供对一个应用内的用户权限的相关读操作
 */
'use strict';
import { Application } from 'egg';

export default (app: Application) => {
  const { router, middleware, controller } = app;
  const { auth, recordOperation } = middleware;
  const { dynamicIp, formula, column, task, other, taskV2, project, client, appl, taskV3 } = controller.open;
  const { subjectScript } = controller.admin;
  const adminTask = controller.admin.task.base;
  const sourceImage = controller.admin.sourceImage;
  const { client: adminClient, appl: adminAppl } = controller.admin;
  // 对于所有的开放接口需要需要进行appKey,appSecret验证
  const openRouter = router.namespace('/open', auth.appl());

  openRouter.get('/task/getOneById', task.getOneById);
  openRouter.get('/task/getInfo', task.getInfo);
  openRouter.post('/task/create', recordOperation(), task.create);
  openRouter.post('/task/revoke', recordOperation(), task.revoke);
  openRouter.post('/task/prioritizeQueue', recordOperation(), task.prioritizeQueue);
  openRouter.post('/task/callback', recordOperation(), task.callback);
  openRouter.get('/task/getUploadToken', task.getUploadToken);
  // 取待处理任务
  openRouter.get('/task/pop', auth.self(), task.pop);
  // 更新任务结果
  openRouter.post('/task/update', auth.self(), task.update);
  openRouter.post('/task/updateV2', task.updateV2);
  // HTML修复回调
  openRouter.post('/task/updateHtmlFix', taskV2.updateHtmlFix);
  // HTML标题修复回调
  openRouter.post('/task/updateHtmlHeaderFix', taskV2.updateHtmlHeaderFix);
  openRouter.post('/task/proofreadingDocxSingle', taskV3.proofreadingDocxSingle);
  // 更新Word/Fbd预处理任务结果
  openRouter.post('/task/:type/update', auth.self(), task.updateTaskResult);
  // 获取动态ip
  openRouter.get('/dynamic/ip/getOne', auth.self(), dynamicIp.getOne);
  // 获取所有任务
  openRouter.get('/task/getAll', auth.self(), adminTask.getListByAdmin);
  // 获取一个任务下的标注过的公式列表
  openRouter.get('/task/formula/getAll', auth.self(), formula.getAllByTaskId);
  // 获取一个任务下的分栏标注过的图片列表
  openRouter.get('/task/column/getAll', auth.self(), column.getAllProcessedByTaskId);
  // 公式识别
  openRouter.post('/formula/detect', formula.detect);

  openRouter.post('/transfer/html2json', auth.self(), other.html2json);
  openRouter.post('/transfer/html2khtml', auth.self(), other.html2khtml);
  openRouter.post('/transfer/html2docx', auth.self(), other.html2docx);

  openRouter.post('/transfer/math', auth.self(), other.math);

  // 数据清洗 回调
  openRouter.post('/script/preProcessingStatus', subjectScript.subjectScriptCb);
  // json预处理 回调
  openRouter.post('/script/jsonPreProcessedCb', subjectScript.jsonPreProcessedCb);

  // 大文件更新Word/Fbd预处理任务结果
  openRouter.post('/taskV2/update', auth.self(), taskV2.updateTaskResult);
  openRouter.post('/taskV2/updateWordNew', auth.self(), taskV2.updateTaskResultWordNew);

  // ------- AI AGENT -------
  openRouter.post('/task/cleanWordHtml', auth.self(), taskV3.cleanWordHtml);
  openRouter.post('/task/aiFixHtml', auth.self(), taskV3.aiFixHtml);
  openRouter.post('/task/html2wordCallback', auth.self(), taskV3.html2wordCallback);
  openRouter.post('/task/proofreadingDocx', auth.self(), taskV3.proofreadingDocx);
  openRouter.get('/task/queryAiEditStatus', taskV3.queryAiEditStatus);
  openRouter.post('/task/rerunAiEditTask', taskV3.rerunAiEditTask);
  openRouter.post('/task/reprocessWordDocument', taskV3.reprocessWordDocument);
  // ------- AI AGENT -------

  // rpa返回pdf信息使用。
  openRouter.post('/taskV2/updateFile', taskV2.updateFile);

  openRouter.get('/taskV2/test', taskV2.test);

  // 根据项目ID获取项目
  openRouter.post('/project/getProject', project.getProject);

  // 创建项目
  openRouter.post('/project/createProject', project.createProject);

  // 复制项目
  openRouter.post('/project/copyProject', project.copyProject);

  // 导出项目字符数【账单】
  openRouter.post('/project/exportStat', project.exportStat);

  // 修改项目的学科学段
  openRouter.post('/project/changeSubject', project.changeSubject);

  // 创建图片项目
  openRouter.post('/project/createImageProject', project.createImageProject);

  // 更新工单号
  openRouter.post('/project/updateProject', project.updateProject);

  // 工单更新项目名称
  openRouter.post('/project/updateProjectName', project.updateProjectName);

  // 工单更新项目文件
  openRouter.post('/project/setFiles', project.setFiles);

  // 工单删除项目
  openRouter.post('/project/delete', project.delete);

  // html2word 回调
  openRouter.post('/project/word/callback', recordOperation(), project.callback);

  openRouter.get('/image/getOne', recordOperation(), task.getImageHtmlUrl);

  // 公式识别
  openRouter.post('/formula/check', formula.check);
  openRouter.get('/sourceImage/getFinishPreCropTaskList', sourceImage.getFinishPreCropTaskList);

  // 根据拆分前的任务ID获取项目
  openRouter.get('/task/getProjectByParentTask', taskV2.getProjectByParentTask);

  // 检索任务 不分页
  openRouter.get('/task/getList', adminTask.getList);

  // 工单系统创建客户
  openRouter.post('/client/create', client.create);

  // 工单系统创建客户应用
  openRouter.post('/appl/create', appl.create);

  openRouter.post('/client/disable', adminClient.disable);
  openRouter.post('/client/enable', adminClient.enable);
  openRouter.post('/client/delete', adminClient.delete);
  openRouter.post('/application/disable', adminAppl.disable);
  openRouter.post('/application/enable', adminAppl.enable);
  openRouter.post('/application/delete', adminAppl.delete);
  openRouter.post('/client/update', adminClient.update);
  openRouter.post('/client/updateConfig', adminClient.updateConfig);

  // 开放 json 质检
  openRouter.post('/taskV2/validateJson', taskV2.validateJson);

  openRouter.get('/project/getPdfPages', project.getPdfPages);
  openRouter.get('/base/getOssAuth', client.getOssAuth);

  openRouter.post('/project/getListByUpdateTime', project.getListByUpdateTime);
};
