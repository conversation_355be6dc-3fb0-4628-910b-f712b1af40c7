/**
 * @file 管理路由
 * <AUTHOR>
 * @desc 原则上开放接口只提供对一个应用内的用户权限的相关读操作
 */
'use strict';
import { Application } from 'egg';

export default (app: Application) => {
  const { controller, middleware, router } = app;
  const { auth, recordOperation } = middleware;
  const { taskV3 } = controller.open;
  const {
    permission,
    task,
    sourceImage,
    image,
    mark,
    review,
    devHelper,
    role,
    project,
    appl,
    book,
    column,
    bookReview,
    projectReview,
    formula,
    client,
    plan,
    timeLimitUserSubject,
    scriptManage,
    subjectScript,
    authCode,
    split,
    relay,
    statV2,
  } = controller.admin;
  const adminRouter = router.namespace('/admin', auth.login(), auth.permission());

  /* 权限相关================================================开始*/
  adminRouter.get('/permission/getAll', permission.getAll);
  /* 权限相关================================================结束*/

  /* 应用相关================================================开始*/
  adminRouter.get('/application/getList', appl.getList);
  /* 应用相关================================================结束*/

  /* 授权码相关================================================开始*/
  adminRouter.get('/authCode/getCode', authCode.getCode);
  adminRouter.get('/authCode/authCode', authCode.authCode);
  /* 授权码相关================================================结束*/

  /* 用户角色相关================================================开始*/
  adminRouter.get('/role/getAll', role.getAll);
  adminRouter.get('/role/getAllUsersByRole', role.getAllUsersByRole);
  /* 用户角色相关================================================结束*/

  /* 任务管理相关================================================开始*/
  adminRouter.get('/task/getListByAdmin', task.base.getListByAdmin);
  adminRouter.get('/task/getList', task.base.getList);
  adminRouter.post('/task/getUrls', task.base.getUrls);
  adminRouter.get('/task/getStat', task.base.getOverview);
  adminRouter.get('/task/getHistory', task.base.getHistory);
  adminRouter.post('/task/ossFilesPath', task.base.getOssFilesPath);
  adminRouter.post('/task/restart', task.base.restart);
  adminRouter.post('/task/callback', task.callback.callback);
  adminRouter.post('/task/batchCallback', task.callback.batchCallback);
  adminRouter.post('/task/create', task.base.create);
  adminRouter.post('/task/rerun', task.base.rerun);
  adminRouter.post('/task/pdfRerun', task.base.pdfRerun);
  adminRouter.post('/task/columnRerun', task.base.columnRerun);
  adminRouter.post('/task/update', task.base.update);
  adminRouter.post('/task/review/remark', review.remark);
  adminRouter.post('/task/delete', task.base.delete);
  adminRouter.post('/task/image/reorder', task.base.reorderImages);
  adminRouter.post('/task/mark/revoke', mark.revoke);
  adminRouter.post('/task/review/revoke', review.revoke);
  adminRouter.post('/task/column/revoke', column.revoke);
  adminRouter.post('/task/column/restart', column.restart);
  adminRouter.post('/task/column/skip', column.skipColumn);
  adminRouter.post('/task/html/imageHtmlRestart', task.base.imageHtmlRestart);
  adminRouter.post('/task/admin/return', task.base.returnError);
  adminRouter.post('/task/bindBook', task.base.bindBook);
  adminRouter.post('/task/unbindBook', task.base.unbindBook);
  adminRouter.get('/task/stat/getListByUser', task.stat.getListByUser);
  adminRouter.get('/task/stat/getTaskV2ListByUser', task.stat.getTaskV2ListByUser);
  adminRouter.get('/task/stat/getOverview', task.stat.getOverview);
  adminRouter.get('/task/stat/getTaskV2Overview', task.stat.getTaskV2Overview);
  adminRouter.post('/task/stat/exportStat', task.stat.exportStat);
  adminRouter.post('/task/stat/exportStatDev', task.stat.exportStatDev);
  adminRouter.post('/task/stat/exportTaskV2StatByProjectId', task.stat.exportTaskV2StatByProjectId);
  adminRouter.get('/task/stat/calcStat', task.stat.calcStat);
  adminRouter.post('/task/revoke', task.base.revoke);
  adminRouter.post('/task/changeName', task.base.changeName);
  /* 任务管理相关================================================结束*/

  /* word 任务管理相关================================================开始*/
  adminRouter.post('/task/word/create', task.wordTask.create);
  adminRouter.post('/task/word/subTask/create', task.wordTask.subTaskCreate);
  /* word 任务管理相关================================================结束*/

  /* fbd 任务管理相关================================================开始*/
  adminRouter.post('/task/fbd/create', task.fbdTask.create);
  adminRouter.post('/task/fbd/subTask/create', task.fbdTask.subTaskCreate);
  /* fbd 任务管理相关================================================结束*/

  /* html 任务管理相关================================================开始*/
  adminRouter.post('/task/html/create', task.htmlTask.create);
  adminRouter.post('/task/html/subTask/create', task.htmlTask.subTaskCreate);
  /* html 任务管理相关================================================结束*/

  /* 预切图================================================开始*/
  adminRouter.get('/task/sourceImage/getPreCropTaskList', sourceImage.getPreCropTaskList);
  adminRouter.get('/task/sourceImage/getPreCropImages', sourceImage.getPreCropImages);
  adminRouter.post('/task/sourceImage/restartPreCrop', sourceImage.restartPreCrop);
  adminRouter.post('/task/sourceImage/submitPreCrop', sourceImage.submitPreCrop);
  adminRouter.post('/task/sourceImage/finishPreCrop', sourceImage.finishPreCrop);
  adminRouter.post('/task/sourceImage/rotateImage', sourceImage.rotateImage);
  adminRouter.post('/task/sourceImage/batchFinishPreCrop', sourceImage.batchFinishPreCrop);
  /* 预切图================================================结束*/

  /* 通用接口================================================开始*/
  adminRouter.get('/task/getOneById', task.base.getOneById);
  adminRouter.get('/task/image/getOneById', image.getOneById);
  adminRouter.get('/task/image/getUrls', image.getUrls);
  adminRouter.get('/task/image/getOriginalUrls', image.getOriginalUrls);
  adminRouter.post('/task/formula/detect', formula.detect);
  adminRouter.post('/task/formula/detectInside', formula.detectInside);
  adminRouter.get('/getUploadToken', book.getUploadToken);
  adminRouter.get('/task/stat/getOwnList', task.stat.getOwnList);
  adminRouter.all('/separateJson', task.base.separateJson);
  adminRouter.post('/task/getListByIds', task.base.getListByIds);
  adminRouter.get('/getOssInfo', task.taskV2.getOssInfo);
  adminRouter.post('/task/checkHtml', task.base.checkHtml);
  adminRouter.post('/task/restoreMachineHtml', task.base.restoreMachineHtml);
  adminRouter.post('/task/rehtmlFix', task.base.rehtmlFix);
  adminRouter.get('/task/getRecognitionProgress', task.base.getRecognitionProgress);
  adminRouter.post('/task/saveTaskMeta', task.base.saveTaskMeta);
  adminRouter.post('/task/saveTaskFile', task.base.saveTaskFile);
  adminRouter.post('/relay/getImagesPosInImage', relay.getImagesPosInImage);
  adminRouter.post('/task/hasOssData', task.base.hasOssData);
  adminRouter.post('/getInternalJson', task.taskV2.getInternalJson);
  adminRouter.post('/formula/check', controller.open.formula.check);
  adminRouter.post('/task/autoFixHtml', task.base.autoFixHtml);
  adminRouter.post('/task/restoreHtml', task.base.restoreHtml);
  /* 通用接口================================================结束*/

  /* 标注相关================================================开始*/
  adminRouter.post('/task/mark/quit', mark.quit);
  // 标注报错
  adminRouter.post('/task/mark/return', mark.returnError);
  adminRouter.get('/task/mark/getOwnList', mark.getOwnList);
  adminRouter.get('/task/mark/getPendingList', mark.getPendingList);
  adminRouter.get('/task/mark/getPendingCount', mark.getPendingCount);
  adminRouter.post('/task/mark/apply', mark.apply);
  adminRouter.post('/task/mark/formula/save', mark.saveFormula);
  adminRouter.post('/task/mark/image/save', mark.saveImage);
  adminRouter.post('/task/mark/confirm', mark.confirm);
  adminRouter.post('/task/mark/rerunImages', mark.rerunImages);
  /* 标注相关================================================结束*/

  /* 审核相关================================================开始*/
  adminRouter.post('/task/review/quit', review.quit);
  // 审核报错
  adminRouter.post('/task/review/return', review.returnError);
  adminRouter.get('/task/review/getOwnList', review.getOwnList);
  adminRouter.get('/task/review/getJson', review.getJson);
  adminRouter.get('/task/review/getJsonUrls', review.getJsonUrls);
  adminRouter.post('/task/review/regenerate', review.regenerate);
  adminRouter.post('/task/review/changeChapter', review.changeChapter);
  adminRouter.post('/task/refreshChapter', review.refreshChapter);
  adminRouter.get('/task/getChapterStatus', review.getChapterStatus);
  adminRouter.get('/task/review/getPendingList', review.getPendingList);
  adminRouter.get('/task/review/getPendingCount', review.getPendingCount);
  adminRouter.post('/task/review/apply', review.apply);
  adminRouter.post('/task/review/formula/save', review.saveFormula);
  adminRouter.post('/task/review/image/save', review.saveImage);
  adminRouter.post('/task/review/confirm', review.confirm);
  adminRouter.get('/task/review/getKatexHtml', review.getKatexHtml);
  adminRouter.get('/task/review/downloadKatexHtml', review.downloadKatexHtml);

  /* 审核相关================================================结束*/

  /* 测试接口================================================开始*/
  // 仅生成并下载doc
  adminRouter.get('/task/dev/generateDoc', devHelper.generateDoc);
  // 七天客户。仅生成并下载doc
  adminRouter.get('/task/dev/generateDoc7day', devHelper.generateDoc7day);
  // 触发任务统计
  adminRouter.post('/task/dev/triggerStat', devHelper.triggerStat);
  adminRouter.get('/task/dev/htmlToKatexHtml', devHelper.htmlToKatexHtml);
  // html 转 json
  adminRouter.post('/task/dev/htmlToJson', devHelper.htmlToJson);
  // 计算html中统计信息
  adminRouter.post('/task/dev/statHtml', devHelper.statHtml);
  // LaTeX字符串解析
  adminRouter.post('/task/dev/latexParser', devHelper.latexParser);
  /* 测试接口================================================结束*/

  /* 项目管理================================================开始*/
  adminRouter.post('/project/create', project.create);
  adminRouter.post('/project/update', project.update);
  adminRouter.post('/project/delete', project.delete);
  adminRouter.post('/project/revoke', project.revoke);
  adminRouter.get('/project/separate', project.separate);
  adminRouter.post('/project/updatePageCountInfos', project.updatePageCountInfos);
  adminRouter.get('/project/getListByAdmin', project.getListByAdmin);
  adminRouter.post('/project/getUrls', project.getUrls);
  adminRouter.get('/project/getOneById', project.getOneById);
  adminRouter.get('/project/getOneByTaskId', project.getOneByTaskId);
  adminRouter.get('/project/getCatalogBookProjects', project.getCatalogBookProjects);
  adminRouter.post('/project/review/apply', projectReview.apply);
  adminRouter.post('/project/review/quit', projectReview.quit);
  adminRouter.post('/project/review/confirm', projectReview.confirm);
  adminRouter.post('/project/review/reMergeProjectJson', projectReview.reMergeProjectJson);
  adminRouter.get('/project/review/getOwnList', projectReview.getOwnList);
  adminRouter.post('/project/review/checkProjectTask', projectReview.checkProjectTask);
  adminRouter.post('/project/review/applyWordsProject', projectReview.applyWordsProject);
  adminRouter.post('/project/getProjectStat', project.getProjectStat);
  adminRouter.post('/project/html2word', project.html2word);

  /* 项目管理================================================结束*/

  /* 书本管理================================================开始*/
  adminRouter.post('/book/image/upload', book.upload);
  adminRouter.get('/book/image/getAll', book.getAllImages);
  adminRouter.post('/book/image/complete', book.completeUpload);
  adminRouter.post('/book/subTask/create', book.subTaskCreate);
  adminRouter.get('/book/getOneById', book.getOneById);
  adminRouter.get('/book/subTask/getList', task.base.getListByBookId);
  adminRouter.post('/book/subTask/reorder', task.base.reorderInBook);
  adminRouter.post('/book/subTask/swapOrder', task.base.swapOrderInBook);
  adminRouter.get('/book/getCatalog', book.getCatalog);
  /* 书本管理================================================结束*/

  /* 书本审核================================================开始*/
  adminRouter.post('/book/review/apply', bookReview.apply);
  adminRouter.post('/book/review/quit', bookReview.quit);
  adminRouter.post('/book/review/confirm', bookReview.confirm);
  /* 书本审核================================================结束*/

  /* 分栏任务管理================================================开始*/
  adminRouter.post('/task/column/restore', column.restore);
  adminRouter.get('/task/column/getOneById', column.getOneById);
  adminRouter.post('/task/column/apply', column.apply);
  adminRouter.post('/task/column/quit', column.quit);
  adminRouter.get('/task/column/getAll', column.getAllByTaskId);
  adminRouter.post('/task/column/save', column.save);
  adminRouter.get('/book/getPendingListByColumn', column.getPendingBooks);
  adminRouter.get('/task/getPendingListByColumn', column.getPendingTasks);
  adminRouter.get('/task/getOwnListByColumn', column.getOwnTasks);
  /* 分栏任务管理================================================结束*/

  /* 客户管理================================================开始*/
  adminRouter.post('/client/create', client.create);
  adminRouter.post('/application/create', appl.create);
  adminRouter.get('/client/getList', client.getList);
  adminRouter.get('/application/getAllByClient', appl.getAllByClient);
  adminRouter.post('/client/disable', client.disable);
  adminRouter.post('/client/enable', client.enable);
  adminRouter.post('/client/delete', client.delete);
  adminRouter.post('/application/disable', appl.disable);
  adminRouter.post('/application/enable', appl.enable);
  adminRouter.post('/application/delete', appl.delete);
  adminRouter.post('/client/update', client.update);
  adminRouter.post('/client/updateConfig', client.updateConfig);
  adminRouter.get('/client/getConfig', client.getConfig);
  /* 客户管理================================================结束*/

  adminRouter.get('/task/getUnAddApplList', task.base.getUnAddApplList);
  adminRouter.get('/task/getUnAddTaskListPage', task.base.getUnAddTaskListPage);

  /* 生产计划================================================开始*/
  // 计划
  adminRouter.post('/plan/create', plan.base.create);
  adminRouter.post('/plan/update', plan.base.update);
  adminRouter.get('/plan/getOneById', plan.base.getOneById);
  adminRouter.get('/plan/getListPage', plan.base.getListPage);
  adminRouter.get('/plan/getApplList', plan.base.getApplList);
  adminRouter.get('/plan/getTaskListPage', plan.base.getTaskListPage);

  // 流程
  adminRouter.get('/plan/flow/getGroupList', plan.flow.getGroupList);
  adminRouter.get('/plan/flow/getApplList', plan.flow.getApplList);

  // 任务组
  adminRouter.post('/plan/group/create', plan.group.create);
  adminRouter.post('/plan/group/update', plan.group.update);
  adminRouter.get('/plan/group/getOneById', plan.group.getOneById);
  adminRouter.post('/plan/group/addUsers', plan.group.addUsers);
  adminRouter.post('/plan/group/removeUsers', plan.group.removeUsers);
  adminRouter.post('/plan/group/changeManager', plan.group.changeManager);
  adminRouter.get('/plan/group/getUsers', plan.group.getUsers);
  adminRouter.get('/plan/group/getUserStat', plan.group.getUserStat);

  // 任务
  adminRouter.get('/plan/task/getListPage', plan.task.getListPage);
  adminRouter.get('/plan/task/getStat', plan.task.getStat);
  adminRouter.get('/plan/task/getCounts', plan.task.getCounts);
  adminRouter.post('/plan/task/stop', recordOperation(), plan.task.stop);
  adminRouter.post('/plan/task/addToPlan', recordOperation(), plan.task.addToPlan);
  adminRouter.post('/plan/task/moveOut', recordOperation(), plan.task.moveOut);
  adminRouter.post('/plan/task/move', recordOperation(), plan.task.move);

  // 用户
  adminRouter.get('/plan/user/getGroupList', plan.user.getGroupList);
  adminRouter.post('/plan/user/applyTask', plan.user.applyTask);
  adminRouter.post('/plan/user/startTask', plan.user.startTask);
  /* 生产计划================================================结束*/

  /* 限时任务================================================开始*/
  // 管理限时任务
  adminRouter.post('/task/limit/setLimit', task.limit.setLimit);
  adminRouter.post('/task/limit/unsetLimit', task.limit.unsetLimit);

  adminRouter.get('/task/limit/getSubjectList', task.limit.getSubjectList);
  adminRouter.get('/task/limit/getApplList', task.limit.getApplList);
  adminRouter.get('/task/limit/getTaskList', task.limit.getTaskList);

  adminRouter.get('/task/limit/getOwnPendingTaskSubjectList', task.limit.getOwnPendingTaskSubjectList);
  adminRouter.get('/task/limit/getOwnTaskList', task.limit.getOwnTaskList);
  adminRouter.post('/task/limit/apply', task.limit.apply);

  // 用户权限配置
  adminRouter.post('/timeLimitUserSubject/create', timeLimitUserSubject.create);
  adminRouter.post('/timeLimitUserSubject/remove', timeLimitUserSubject.remove);
  adminRouter.get('/timeLimitUserSubject/getList', timeLimitUserSubject.getList);

  /* 限时任务================================================结束*/

  /* 脚本================================================开始*/
  adminRouter.get('/script/getList', scriptManage.getScripts);
  adminRouter.post('/script/run', scriptManage.runScripts);

  adminRouter.get('/script/getSubjectScriptList', subjectScript.getSubjectScriptList);

  adminRouter.get('/script/getSubjectScriptConfigList', subjectScript.getSubjectScriptConfigList);
  adminRouter.post('/script/addSubjectScriptConfig', subjectScript.addSubjectScriptConfig);
  adminRouter.post('/script/updateSubjectScriptConfig', subjectScript.updateSubjectScriptConfig);
  adminRouter.post('/script/delSubjectScriptConfig', subjectScript.delSubjectScriptConfig);
  adminRouter.post('/script/runSubjectScript', subjectScript.runSubjectScript);
  /* 脚本================================================结束*/

  /** 大文件任务流程====================================开始*/
  adminRouter.post('/taskV2/wordCreate', task.taskV2.wordCreate);
  adminRouter.post('/taskV2/pdfCreate', task.taskV2.pdfCreate);
  adminRouter.post('/taskV2/fbdCreate', task.taskV2.fbdCreate);
  adminRouter.post('/taskV2/pdf2images', task.taskV2.pdf2images);
  adminRouter.get('/taskV2/getOneById', task.taskV2.getOneById);
  adminRouter.post('/taskV2/markConfirm', task.taskV2.markConfirm);
  adminRouter.post('/taskV2/reviewConfirm', task.taskV2.reviewConfirm);
  adminRouter.post('/taskV2/reviewChangeChapter', task.taskV2.changeChapter);
  adminRouter.post('/taskV2/refreshChapter', task.taskV2.refreshChapter);
  adminRouter.post('/taskV2/reviewRegenerate', task.taskV2.regenerate);
  adminRouter.get('/taskV2/reviewGetJsonUrls', review.getJsonUrls);
  adminRouter.post('/taskV2/restart', task.taskV2.restart);
  adminRouter.get('/task/queryAiEditStatus', taskV3.queryAiEditStatus);
  adminRouter.post('/task/rerunAiEditTask', taskV3.rerunAiEditTask);
  adminRouter.post('/taskV2/save', task.taskV2.save);
  adminRouter.post('/taskV2/analysisChapterLevel', task.taskV2.analysisChapterLevel);
  adminRouter.post('/taskV2/mergePreHandleHtml', task.taskV2.mergePreHandleHtml);
  adminRouter.post('/taskV2/generateMachineDocx', task.taskV2.generateMachineDocx);
  adminRouter.post('/taskV2/updateTaskPage', task.taskV2.updateTaskPage);

  /** 大文件任务流程====================================结束*/

  /* 大文件分割流程========================================开始*/
  adminRouter.get('/task/getOwnListBySplit', split.getOwnListBySplit);
  adminRouter.get('/task/getPendingListBySplit', split.getPendingListBySplit);
  adminRouter.post('/task/split/apply', split.apply);
  adminRouter.post('/task/split/quit', split.quit);
  adminRouter.post('/task/split/reduct', split.reduct);
  adminRouter.post('/task/split/jump', split.jump);
  adminRouter.post('/task/split/confirm', split.confirm);
  adminRouter.post('/task/split/splitPdf', split.splitPdf);
  adminRouter.post('/task/split/splitPdf/rotateImage', split.rotatePdfImage);
  adminRouter.post('/task/split/splitPdf/setAnswer', split.setAnswer);
  adminRouter.post('/task/split/splitPdf/chunkImage', split.chunkImage);
  adminRouter.post('/task/split/splitPdf/rotateImageV2', split.rotateImageV2);
  adminRouter.post('/task/split/splitPdf/saveEdit', split.saveEdit);
  adminRouter.post('/task/split/splitPdf/splitPdfImageMove', split.splitPdfImageMove);
  adminRouter.post('/task/split/splitPdf/rovokeChunkPdfImgs', split.rovokeChunkPdfImgs);
  adminRouter.post('/task/split/imageConfirm', split.imageConfirm);
  adminRouter.post('/task/split/revoke', split.revoke);
  adminRouter.post('/task/split/saveSample', split.saveSample);
  adminRouter.get('/task/split/getSample', split.getSample);

  adminRouter.post('/taskV2/imageTaskSplit', split.imageTaskSplit);
  /* 大文件分割流程========================================结束*/

  /* 工时统计看板========================================结束*/
  adminRouter.post('/statV2/begin', statV2.begin);
  adminRouter.post('/statV2/end', statV2.end);
  adminRouter.post('/statV2/setProjectPdfCount', statV2.setProjectPdfCount);
  adminRouter.post('/statV2/getTasksStat', statV2.getTasksStat);
  adminRouter.post('/statV2/getProjectsStat', statV2.getProjectsStat);
  // adminRouter.post('/statV2/getUserStat', statV2.getUserStat);

  adminRouter.get('/base/getOssAuth', client.getOssAuth);

  adminRouter.get('/task/getPDFPageNum', task.base.getPDFPageNum);
};
