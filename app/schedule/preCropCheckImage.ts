/**
 * @file 检查任务图片是否需要手工切图
 */

'use strict';
import { Context } from 'egg';
import BaseSubscription from '../core/base/baseSubscription';

export default class PreCropCheckImageTask extends BaseSubscription {
  constructor(ctx: Context) {
    super(ctx, 'pool', ctx.app.config.scheduleLockKey.preCropCheckImage, 2);
  }

  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      interval: '1s',
      type: 'worker',
      disable: false,
      env: ['dev','pre', 'prod'],
    };
  }

  public async start() {
    if (process.argv.some((p) => p.includes('xdoc-server'))) {
      return;
    }
    const { service, logger, ctx } = this;
    const taskId = await service.task.base.popPreCropCheck();
    if (!taskId) {
      return;
    }
    const runningTime = Number(new Date());
    logger.info(`[预切图检查] ${taskId} pre-crop images start`);
    const { statuses } = service.task.base;
    try {
      const task = await service.task.base.getOne({ where: { taskId, status: statuses.preCropCheck } });

      if (!task) {
        logger.info(`检查任务图片是否需要手工切图错误，不是检查状态 ${taskId} `);
        return;
      }
      const images = await service.sourceImage.getAll({
        where: { taskId },
        attributes: ['imageId', 'appKey', 'bookId', 'filename', 'taskOrder', 'id'],
      });
      const infos = await Promise.all(images.map(async(item) =>
        service.image.getImageInfo(item.appKey, item.imageId)));
      logger.info(`[预切图检查] ${taskId} 获取图片信息 done.`);
      images.forEach((image, index) => {
        const info = infos[index];
        if (!info) throw new Error('存在没有 info 数据的 source image');
        image.info = JSON.stringify(info);
      });
      const imageData = images.map((image) => {
        return { imageId: image.imageId, info: image.info, appKey: image.appKey, id: image.id };
      }).filter((item) => Boolean(item.info));
      if (imageData.length) { // 更新原图大小信息
        /*
         * Tips：
         * 由于 sourceImage 的唯一索引取消掉，
         * 所以这里不建议继续使用 bulkCreate 方法配合 updateOnDuplicate 使用。
         * await service.sourceImage.bulkCreate(imageData as any, { updateOnDuplicate: ['info'] });
         */
        await Promise.all(imageData.map((d) =>
          service.sourceImage.update(
            { info: d.info },
            { where: { id: d.id } }
            /*
             * Tips：
             * 避免 Deadlock found when trying to get lock; try restarting transaction 问题
             * 更新非主键索引引起的死锁
             * 非主键索引、存在多条数据、多线程并发，导致死锁
             * { where: { id: d.imageId, appKey: d.appKey, taskId } },
             */
          )));
      }
      logger.info(`[预切图检查] ${taskId} 更新原图大小信息 done.`);
      const needCrop = infos.some((info) => info && (info.w >= info.h));
      logger.info(`[预切图检查] ${taskId} needCrop:${needCrop} `);
      if (needCrop) { // 如果存在横向图片，需要手工切图
        await service.task.base.update({ status: statuses.preCropChecked }, { where: { taskId } });
      } else { // 如果都是竖直方向图片，进入下个流程。将 source_image 同步到 image 中
        await service.task.base.finishPreCropImage(taskId, images, task.rerun);
      }
      // 记录操作日志
      ctx.runInBackground(async() => {
        await service.task.history.create({
          taskId,
          userId: service.user.aiFilterUserId,
          costTime: 0,
          type: service.task.history.preCropTypes.check.id,
          data: JSON.stringify({ needCrop }),
        });
      });
    } catch (e) {
      service.task.base.update({
        status: statuses.error,
        errorInfo: (e as any).message,
      }, { where: { taskId } });
    }

    this.app.logRunningTime(runningTime, `preCropCheckImage tasks:${JSON.stringify(taskId)}`);
  }
}
