'use strict';
import axios from 'axios';
import { Controller } from 'egg';
import { htmlToJsonV5 } from '../../core/utils/aiEdit/htmlToJsonAI';
import baseError from '../../core/base/baseError';
import WordAnnotationMannger from '../../core/utils/aiEdit/WordAnnotationMannger';
import { flattenByLevelOneQuestions, json2Jpg } from '../../core/utils/treeHelper';
import { iterateNode } from '../../core/utils/treeHelper';

export default class OpenTaskV3Controller extends Controller {
  // 这条线现在只用来做 wps 单题模式，不需要 xdoc 人工流程
  public async cleanWordHtml() {
    //  html 清洗（去掉 😊xxx😊）
    // 入队，做 ai 多轮修复
    const { ctx, service, logger, config } = this;
    try {
      // appkey 默认合心
      const { word_url: wordUrl, subject, stage, appkey: appKey, task_id: uid, word_type: wordType } = ctx.input;
      // status: 0 成功
      const { html_url: htmlUrl } = ctx.input.data;

      logger.info(`[cleanWordHtml] 参数: ${JSON.stringify(ctx.input)}`);
      const html = await service.ai.httpGetWithRetry(htmlUrl);

      // 清洗
      const cleanHtml = service.ai.cleanHTMLSymbol(html.data);
      const htmlKey = service.task.base.getAiOssKey(appKey, uid, 'html');
      const backupKey = service.task.base.getAiOssKey(appKey, uid, 'backup.html');
      // 上传到 oss
      await service.oss.upload(htmlKey, cleanHtml, 'string');
      await service.oss.upload(backupKey, cleanHtml, 'string');
      const cleanedHtmlUrl = service.task.base.getAiUrl(appKey, uid, 'html', true);
      // 入队
      await service.rbs.initRBSQueue({
        task_id: uid,
        task_level: 0,
        task_type: 'xdoc_html_fix_priority',
        task_info: {
          task_id: uid,
          word_url: wordUrl,
          html_url: cleanedHtmlUrl,
          is_ai_edit: true,
          subject,
          stage,
          app_key: appKey,
          word_type: wordType,
          bucket_name: config.aliOss.bucket,
          upload_path: service.task.base.getAiOssKey(appKey, uid, 'fixed.html'),
          get_task_parameters: [
            'task_id',
            'is_ai_edit',
            'html_url',
            'word_url',
            'subject',
            'stage',
            'app_key',
            'word_type',
            'bucket_name',
            'upload_path'
          ],
          callback_extras: [
            'task_id',
            'html_url',
            'subject',
            'stage',
            'word_url',
            'app_key',
            'word_type',
            'bucket_name',
            'upload_path'
          ],
          run_type: 'common',
          callback_url: `${config.ngrok.callbackUrl}/api/open/task/aiFixHtml`,
          push_time: new Date().getTime(),
          timestamp: new Date().getTime(),
        },
      });
      logger.info(`[cleanWordHtml] 入队: ${JSON.stringify({
        task_id: uid,
        word_url: wordUrl,
        html_url: cleanedHtmlUrl,
        subject,
        stage,
        app_key: appKey,
        word_type: wordType,
        bucket_name: config.aliOss.bucket,
        upload_path: service.task.base.getAiOssKey(appKey, uid, 'fixed.html'),
        get_task_parameters: [
          'task_id',
          'html_url',
          'subject',
          'stage',
          'app_key',
          'word_type',
          'bucket_name',
          'upload_path'
        ],
        callback_extras: [
          'task_id',
          'html_url',
          'subject',
          'stage',
          'word_url',
          'app_key',
          'word_type',
          'bucket_name',
          'upload_path'
        ],
        run_type: 'common',
        callback_url: `${config.ngrok.callbackUrl}/api/open/task/aiFixHtml`,
        push_time: new Date().getTime(),
        timestamp: new Date().getTime(),
      })}`);

      // 添加超时监控
      const taskParams = service.ai.formatTaskParams({
        taskId: uid,
        appKey,
        subject,
        stage,
        wordType,
        htmlUrl,
        wordUrl,
      });

      await service.timeout.addTimeoutTask(
        uid,
        'html_fix',
        // 10分钟超时
        1.5 * 60 * 1000,
        {
          title: 'HTML修复任务超时',
          content: `任务ID: ${uid} 的HTML修复任务执行超时，请检查AI修复服务状态`,
          receiveId: 4,
          extra: taskParams,
        }
      );

      return ctx.body = { status: 0 };
    } catch (err) {
      logger.error(`[cleanWordHtml] 失败: ${err}`);
      return ctx.body = baseError.serverError('cleanWordHtml');
    }
  }

  public async aiFixHtml() {
    const stageMapping = {
      junior: 'cz',
      senior: 'gz',
    };
    const subjectMapping = {
      biology: 'shengwu',
      chemistry: 'huaxue',
      chinese: 'yuwen',
      computer_science: 'jisuanji',
      daode_fazhi: 'daofa',
      english: 'yingyu',
      geography: 'dili',
      history: 'lishi',
      math: 'shuxue',
      physics: 'wuli',
      science: 'kexue',
    };

    // 接受 ai 修复后的 htmlUrl,
    // 转成 json
    const { ctx, service, logger, app, config } = this;
    // 解析回调数据
    const { word_url: wordUrl, subject, stage, word_type: wordType, app_key: appKey } = ctx.input;
    const {
      task_id,
      status,
      error,
      result,
      stat,
      // word_url: wordUrl,
      // subject,
    } = (ctx.input?.data || {}) as {
      task_id: string;
      // 0 成功 1 失败
      status: number;
      // 如果报错，这里存储错误信息
      error: string;
      // 上传的路径
      result: string;
      // 统计数据
      stat: any;
      word_url: string;
      app_key: string;
      subject: string;
      stage: string;
    };

    logger.info(`[aiFixHtml]: ${JSON.stringify({ task_id, status, error, result, wordUrl, appKey, stat, subject, stage, wordType })}`);

    // 获取任务信息（这里使用AI任务ID，不是正式任务）
    const taskId = task_id;

    try {
      // 释放超时监控
      await service.timeout.releaseTimeoutTask(taskId, 'html_fix');

      // 统计数据记录
      if (stat) {
        logger.info(`[aiFixHtml] 统计数据: ${JSON.stringify(stat)}`);
        // 更新项目状态
        await service.ai.callbackTokens(taskId, 'html_fix', stat.total.cost_token, stat);
      }

      // 根据回调结果处理
      if (status === 1) {
        logger.error(`[aiFixHtml] 修复失败: ${JSON.stringify(error)}`);

        // 发送错误通知到工单系统
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            ticket: taskId,
            business_project_id: taskId,
            status: 1,
            result: {},
            reason: error || '未知错误',
          }),
        });

        return ctx.body = { status: 0 };
      }

      // 修复成功的处理
      logger.info(`[aiFixHtml] 修复成功: ${taskId}`);

      if (result) {
        try {
          const htmlUrl = service.task.base.getAiUrl(appKey, taskId, 'fixed.html', true);
          // 更新 ai 修复的结果
          logger.info(`[aiFixHtml] 获取修复后的 HTML: ${htmlUrl}`);
          // await service.oss.upload(htmlUrl, result, 'string');
          const htmlRes = await service.ai.httpGetWithRetry(htmlUrl);
          logger.info(`[aiFixHtml] 获取修复后的 HTML 成功: ${taskId}`);
          const html = htmlRes.data;
          // 转 json
          let json: any[] = htmlToJsonV5({ html, isSingleQuest: wordType.includes('wps') });
          // 刷一下 json 数据结构
          const isCheck = wordType.includes('check');
          json = await service.ai.formatJSON(json, isCheck ? 'check' : 'analysis');
          logger.info(`[aiFixHtml] json 结构化问题处理成功: ${taskId}`);
          const aiFormattedJson = flattenByLevelOneQuestions(json).map((node) => {
            if (node.node_type === 'paragraph' && !node.is_image_node) {
              return { ...node, node_type: 'question', question_type: 'other' };
            }
            return node;
          }).filter((v) => v.node_type === 'question');
          await json2Jpg(aiFormattedJson, isCheck ? 'check' : 'analysis');
          logger.info(`[aiFixHtml] json 转图成功: ${taskId}`);
          // 上传到 oss
          const jsonKey = service.task.base.getAiOssKey(appKey, taskId, 'fixed.json');
          const jsonUrl = service.task.base.getAiUrl(appKey, taskId, 'fixed.json', true);
          await service.oss.upload(jsonKey, JSON.stringify(aiFormattedJson), 'string');

          logger.info(`[aiFixHtml] 上传成功: taskId: ${taskId} jsonUrl: ${jsonUrl}`);
          // 进入校对环节
          await service.rbs.initRBSQueue({
            task_id: taskId,
            task_type: 'ai_edit',
            task_info: {
              task_id: taskId,
              word_url: wordUrl,
              html_url: htmlUrl,
              json_url: jsonUrl,
              app_key: appKey,
              subject: `${stageMapping[stage] || 'cz'}_${subjectMapping[subject]}`,
              word_type: (wordType || '')?.split('wps-').join(''),
              get_task_parameters: [
                'task_id',
                'word_url',
                'html_url',
                'json_url',
                'subject',
                'stage',
                'app_key',
                'word_type'
              ],
              callback_extras: [
                'task_id',
                'word_url',
                'html_url',
                'app_key',
                'subject',
                'stage',
                'word_type'
              ],
              run_type: 'common',
              callback_url: `${config.ngrok.callbackUrl}/api/open/task/proofreadingDocx`,
              push_time: new Date().getTime(),
              timestamp: new Date().getTime(),
            },
          });
          // 添加超时监控
          const taskParams = service.ai.formatTaskParams({
            taskId,
            appKey,
            subject,
            stage,
            wordType,
            htmlUrl,
            jsonUrl,
            wordUrl,
          });

          await service.timeout.addTimeoutTask(
            taskId,
            'ai_edit',
            // 2分钟超时
            6 * 60 * 1000,
            {
              title: 'ai-edit任务超时',
              content: `wps-任务ID: ${taskId} 的ai-edit任务执行超时，请检查ai-edit服务状态`,
              receiveId: 4,
              extra: taskParams,
            }
          );
          logger.info(`[aiFixHtml] 进入校对环节，入队成功: taskId: ${taskId} wordType: ${(wordType || '')?.split('wps-').join('')}`);
          return ctx.body = { status: 0 };
        } catch (copyError) {
          logger.error(`[aiFixHtml] 处理失败: ${copyError}`);
          // 处理失败通知到工单系统
          await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
            method: 'POST',
            dataType: 'json',
            data: JSON.stringify({
              ticket: taskId,
              business_project_id: taskId,
              status: 1,
              result: {},
              reason: `HTML处理失败: ${copyError}`,
            }),
          });

          return ctx.body = baseError.serverError(`处理失败: ${copyError}`);
        }
      }
      logger.error('[aiFixHtml] 修复成功但没有返回result路径');

      // 即使没有result，依然尝试进入下一流程
      await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket: taskId,
          business_project_id: taskId,
          status: 1,
          result: {},
          reason: '修复成功但没有返回result路径',
        }),
      });

      return ctx.body = { status: 0 };
    } catch (err) {
      logger.error(`[aiFixHtml] 报错 : taskId: ${taskId} error: ${err}`);

      // 发送错误到工单系统
      await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket: taskId,
          business_project_id: taskId,
          status: 1,
          result: {},
          reason: `${err}`,
        }),
      });

      return ctx.body = baseError.serverError('aiFixHtml');
    }
  }

  public async proofreadingDocx() {
    // 校对并添加批注到 Word 文档
    const { ctx, service, logger, app } = this;
    const {
      word_url: wordUrl,
      html_url: htmlUrl,
      app_key: appKey,
      word_type: wordType,
    } = ctx.input;
    const {
      task_id: taskId,
      status,
      info,
      json,
    } = (ctx.input?.data || {}) as {
      task_id: string;
      // 0 成功 1 失败
      status: number;
      // 如果报错，这里存储报错信息
      info: string;
      json: any[];
    };
    logger.info(`[proofreadingDocx]: ${JSON.stringify({ taskId, status, info, wordUrl, htmlUrl, appKey, wordType })}`);
    await service.timeout.releaseTimeoutTask(taskId, 'ai_edit');
    try {
      if (status === 1 || status === 3) {
        logger.error(`[proofreadingDocx] 失败: taskId: ${taskId} info: ${info}`);
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            ticket: taskId,
            business_project_id: taskId,
            status: 1,
            result: {},
            reason: info,
          }),
        });
        return ctx.body = { status: 0 };
      }

      // 解析 JSON
      const jsonData: any[] = Array.isArray(json) ? json : JSON.parse(json);

      // WPS 类型直接上传 JSON 文件
      if (wordType.includes('wps') || wordType.includes('analysis') || wordType.includes('check')) {
        logger.info(`[proofreadingDocx] WPS 类型，直接上传 JSON: taskId: ${taskId}`);
        // 上传到 oss
        const jsonKey = service.task.base.getAiOssKey(appKey, taskId, 'wps.json');
        await service.oss.upload(jsonKey, JSON.stringify(jsonData), 'string');
        if (wordType.includes('check')) {
          // 校对不需要 html_docx
          return ctx.body = { status: 0 };
        }
        const jsonResultUrl = service.task.base.getAiUrl(appKey, taskId, 'wps.json', true);

        logger.info(`[proofreadingDocx] WPS JSON 上传成功: ${jsonResultUrl}`);

        // 将jsonData转换为HTML
        let htmlContent = '';

        // 处理jsonData中的node
        const processAgentInfo = async (node: any) => {
          const nodeHtml = await WordAnnotationMannger.processAgentInfoToHtml(node, {
            checkDuplicatePrefix: true,
            includeEmptyFallback: true,
          });
          htmlContent += nodeHtml;
        };

        // 遍历jsonData
        for (const item of jsonData) {
          if (item.mode1 && !item.mode2) {
            for (const node of item.mode1) {
              await processAgentInfo(node);
            }
          } else if (item.mode1 && item.mode2) {
            for (const node of item.mode1) {
              await processAgentInfo(node);
            }
            for (const node of item.mode2) {
              await processAgentInfo(node);
            }
          }
        }
        try {
          const tokens = jsonData.reduce((p, _json: any) => p + _json.mode1[0].tokens, 0);
          logger.info(`[proofreadingDocx]  token : ${tokens}`);
          await service.ai.callbackTokens(taskId, 'ai_edit', tokens, {});
        } catch (e) {
          logger.info(`[proofreadingDocx] 回调 token 失败: ${e}`);
        }

        // 上传HTML到OSS
        const htmlKey = service.task.base.getAiOssKey(appKey, taskId, 'wps.html');
        await service.oss.upload(htmlKey, htmlContent, 'string');

        // 入队html2word
        const _taskId = `${taskId}_${appKey}`;
        await service.rbs.initRBSQueue({
          task_id: _taskId,
          task_type: 'html_docx',
          task_level: 0,
          task_info: {
            projectId: _taskId,
            jobName: 'generateDocxFromHtml',
            queue: true,
            params: {
              html: htmlContent,
              appKey,
              path: `${taskId}.wps.docx`,
              config: {
                default_style: {
                  font_size: '10.5pt',
                  font_family: {
                    east_asia: 'SimSun',
                    latin: 'Times New Roman',
                  },
                  line_height: 1.2,
                  color: '0000FF',
                }, disable_choice_layout: false,
              },
              taskId: _taskId,
              output: 'url',
            },
            callback: {
              url: `${this.config.ngrok.callbackUrl}/api/open/task/html2wordCallback`,
              method: 'POST',
              returnProps: ['projectId', 'status', 'result', 'reason'],
              successStatus: {
                prop: 'status',
                value: [0],
              },
            },
            push_time: new Date().getTime(),
            timestamp: new Date().getTime(),
          },
        });
        const taskParams = service.ai.formatTaskParams({
          taskId: _taskId,
          appKey,
          wordType,
          htmlUrl,
          jsonResultUrl,
          wordUrl,
        });

        await service.timeout.addTimeoutTask(
          _taskId,
          'html_docx',
          2 * 60 * 1000,
          {
            title: 'HTML转Word任务超时',
            content: `任务ID: ${_taskId} 的HTML转Word任务执行超时，请检查HTML转Word服务状态`,
            receiveId: 4,
            extra: taskParams,
          }
        );

        logger.info(`[proofreadingDocx] 入队html2word成功: taskId: ${_taskId}`);

        // 提交到工单交付物
        // await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        //   method: 'POST',
        //   dataType: 'json',
        //   data: JSON.stringify({
        //     ticket: taskId,
        //     business_project_id: taskId,
        //     status: 0,
        //     result: jsonResultUrl,
        //     reason: '',
        //   }),
        // });
        return ctx.body = { status: 0 };
      }

      // 下载 word，分析 json
      const manager = new WordAnnotationMannger(wordUrl);
      await manager.loadDocument();
      // 处理mode节点
      async function processModeNode(node: any) {
        await manager.processErrorInfo(node);
        await manager.processAgentInfo(node);
      }

      for (const item of jsonData) {
        // 如果只有mode1，处理mode1
        if (item.mode1 && !item.mode2) {
          for (const node of item.mode1) {
            await processModeNode(node);
          }
        } else if (item.mode1 && item.mode2) {
          // 如果同时有mode1和mode2，两者都需要处理
          // 处理mode1
          for (const node of item.mode1) {
            await processModeNode(node);
          }
          // 处理mode2
          for (const node of item.mode2) {
            await processModeNode(node);
          }
        }
      }
      manager.removeAllCustomIdsAndMarkers();
      // 保存文档
      const docxBuffer = await manager.saveDocument();
      // 上传到 oss
      const docxKey = service.task.base.getAiOssKey(appKey, taskId, 'done.docx');
      await service.oss.upload(docxKey, docxBuffer);
      const wordResultUrl = service.task.base.getAiUrl(appKey, taskId, 'done.docx', true);
      logger.info(`[proofreadingDocx]: 上传成功: ${wordResultUrl}`);
      // 提交到工单交付物
      await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket: taskId,
          // 这个也可以传工单 id
          business_project_id: taskId,
          // 状态，可以定 0 成功  1 失败
          status: 0,
          // word url
          result: {
            word_url: wordResultUrl,
            question_count: 1,
          },
          // 失败的时候的失败信息
          reason: '',
        }),
      });
      return ctx.body = { status: 0 };
    } catch (err) {
      logger.error(`proofreadingDocx 报错 : taskId: ${taskId} error: ${err}`);
      await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket: taskId,
          business_project_id: taskId,
          status: 1,
          result: {},
          reason: err,
        }),
      });
      return ctx.body = baseError.serverError('proofreadingDocx');
    }
  }

  public async proofreadingDocxSingle() {
    // 处理单个json的回调
    const { ctx, service, logger, app } = this;
    const {
      word_url: wordUrl,
      html_url: htmlUrl,
      app_key: appkey,
      word_type: wordType,
    } = ctx.input;
    const {
      task_id: taskId,
      status,
      info,
      json,
    } = (ctx.input?.data || {}) as {
      task_id: string;
      status: number;
      info: string;
      json: any[];
    };
    logger.info(`[proofreadingDocxSingle]: ${JSON.stringify({ taskId, status, info, wordUrl, htmlUrl, appkey, wordType })}`);
    try {
      if (status === 1) {
        logger.error(`[proofreadingDocxSingle] 失败: taskId: ${taskId} info: ${info}`);
        return ctx.body = { status: 0 };
      }
      await service.timeout.releaseTimeoutTask(taskId, 'ai_edit_single');

      // 解析 JSON
      const jsonData: any[] = Array.isArray(json) ? json : JSON.parse(json);

      // 上传到 oss
      const jsonKey = service.task.base.getAiOssKey(appkey, taskId, 'ai.process.json');
      await service.oss.upload(jsonKey, JSON.stringify(jsonData), 'string');
      const jsonResultUrl = service.task.base.getAiUrl(appkey, taskId, 'ai.process.json', true);

      logger.info(`[proofreadingDocxSingle] JSON 上传成功: ${jsonResultUrl}`);

      // 记录到redis
      const redisKey = `ai_edit_single:${taskId}`;
      await app.redis.set(redisKey, '1');

      // 检查是否所有json都处理完成
      const projectId = taskId.split('-')[0];
      const allKeys = await app.redis.keys(`ai_edit_single:${projectId}-*`);
      const allValues = await Promise.all(allKeys.map((key) => app.redis.get(key)));
      const allProcessed = allValues.every((value) => value === '1' || value !== '0');

      if (allProcessed) {
        // 所有json都处理完成，开始处理word
        const manager = new WordAnnotationMannger(wordUrl);
        await manager.loadDocument();

        // 处理mode节点
        async function processModeNode(node: any) {
          await manager.processErrorInfo(node);
          await manager.processAgentInfo(node);
        }

        // 获取所有处理完成的json
        const allJson: any = (await Promise.all(allKeys.map(async(key) => {
          try {
            // 先检查状态，只处理状态为'1'的任务
            const value = await app.redis.get(key);
            if (value !== '1') {
              // 排除掉非'1'状态（包括'-3'状态）
              return null;
            }

            const _jsonUrl = service.task.base.getAiUrl(appkey, key.replace('ai_edit_single:', ''), 'ai.process.json', true);
            const jsonContent = await service.oss.fetch(_jsonUrl, 'json');
            return jsonContent;
          } catch (error) {
            logger.error(`[proofreadingDocxSingle] 获取JSON失败: ${key}, ${error}`);
            return null;
          }
          // 过滤掉null值
        }))).filter(Boolean);

        logger.info(`[proofreadingDocxSingle] 获取所有处理完成的json: ${allJson.length}个`);
        const allJsonData = allJson.flat();
        logger.info(`[proofreadingDocxSingle] 处理后的json数据量: ${allJsonData.length}个条目`);

        // 上传
        const key = service.task.base.getAiOssKey(appkey, projectId, 'ai.process.json');
        await service.oss.upload(key, JSON.stringify(allJsonData), 'string');

        // 处理所有json
        for (const item of allJsonData) {
          if (item.mode1 && !item.mode2) {
            for (const node of item.mode1) {
              try {
                await processModeNode(node);
              } catch (error) {
                logger.error(`[proofreadingDocxSingle] 处理节点失败: ${node.id}, ${error}`);
              }
            }
          } else if (item.mode1 && item.mode2) {
            for (const node of item.mode1) {
              try {
                await processModeNode(node);
              } catch (error) {
                logger.error(`[proofreadingDocxSingle] 处理节点失败: ${node.id}, ${error}`);
              }
            }
            for (const node of item.mode2) {
              try {
                await processModeNode(node);
              } catch (error) {
                logger.error(`[proofreadingDocxSingle] 处理节点失败: ${node.id}, ${error}`);
              }
            }
          }
        }
        try {
          const tokens = allJsonData.reduce((_json: any) => _json.mode1.tokens, 0);
          logger.info(`[proofreadingDocx]  token : ${tokens}`);
          await service.ai.callbackTokens(taskId, 'ai_edit', tokens, {});
        } catch (e) {
          logger.info(`[proofreadingDocx] 回调 token 失败: ${JSON.stringify(e)}`);
        }

        manager.removeAllCustomIdsAndMarkers();
        // 保存文档
        const docxBuffer = await manager.saveDocument();
        // 上传到 oss
        const docxKey = service.task.base.getAiOssKey(appkey, projectId, 'done.docx');
        await service.oss.upload(docxKey, docxBuffer);
        const wordResultUrl = service.task.base.getAiUrl(appkey, projectId, 'done.docx', true);
        logger.info(`[proofreadingDocxSingle]: 上传成功: ${wordResultUrl}`);
        const project = await service.project.base.getOne({ where: { id: projectId } });

        // 提交到工单交付物
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            ticket_id: project?.workOrder || '',
            business_project_id: projectId,
            status: 0,
            result: {
              word_url: wordResultUrl,
              question_count: allJsonData.length,
            },
            reason: '',
          }),
        });
        logger.info(`[proofreadingDocxSingle]: 上传成功: ${JSON.stringify({
          ticket_id: project?.workOrder || '',
          business_project_id: projectId,
          status: 0,
          result: wordResultUrl,
          reason: '',
        })}`);

        // 清理redis
        await app.redis.del(...allKeys);
      }
      return ctx.body = { status: 0 };
    } catch (err) {
      logger.error(`proofreadingDocxSingle 报错 : taskId: ${taskId} error: ${err}`);
      return ctx.body = baseError.serverError('proofreadingDocxSingle');
    }
  }

  /**
   * 查询项目下每个json题目的处理状态
   */
  public async queryAiEditStatus() {
    const { ctx, app, service, logger } = this;
    const { projectId } = ctx.input;

    if (!projectId) {
      return ctx.body = baseError.paramsError('缺少必要参数：projectId');
    }

    try {
      // 获取项目下所有的 redis 键
      const allKeys = await app.redis.keys(`ai_edit_single:${projectId}-*`);
      if (!allKeys.length) {
        return ctx.body = { status: 0, data: { tasks: [] } };
      }

      // 获取所有键对应的值
      const values = await Promise.all(allKeys.map(async(key) => {
        const value = await app.redis.get(key);
        const nodeId = key.replace(`ai_edit_single:${projectId}-`, '').split('-')[0];
        const index = key.replace(`ai_edit_single:${projectId}-`, '').split('-')[1];

        // 处理不同的状态值
        let status = 'processing';
        let statusCode = 0;

        if (value === '1') {
          status = 'completed';
          statusCode = 1;
        } else if (value === '-1') {
          status = 'failed_once';
          statusCode = -1;
        } else if (value === '-2') {
          status = 'failed_twice';
          statusCode = -2;
        } else if (value === '-3') {
          status = 'failed_connectivity';
          statusCode = -3;
        }

        return {
          node_id: nodeId,
          task_id: `${projectId}-${nodeId}-${index}`,
          status,
          status_code: statusCode,
        };
      }));

      // 获取项目信息
      const project = await service.project.base.getOne({ where: { id: projectId } });
      if (!project) {
        logger.error(`[queryAiEditStatus] 项目不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }

      // 计算各类任务数量
      const completedCount = values.filter((v) => v.status_code === 1).length;
      const failedCount = values.filter((v) => v.status_code < 0).length;
      const processingCount = values.filter((v) => v.status_code === 0).length;

      // 判断整体任务状态
      const isAllDone = processingCount === 0;

      return ctx.body = {
        status: 0,
        data: {
          project_id: projectId,
          project_name: project.projectName,
          total: values.length,
          completed: completedCount,
          failed: failedCount,
          processing: processingCount,
          is_all_done: isAllDone,
          tasks: values,
        },
      };
    } catch (err) {
      logger.error(`[queryAiEditStatus] 错误: ${err}`);
      return ctx.body = baseError.serverError(`查询状态失败: ${err}`);
    }
  }

  /**
   * 重新入队特定题目的AI编辑任务
   */
  public async rerunAiEditTask() {
    const { ctx, app, service, config, logger } = this;
    const { projectId, nodeId, index } = ctx.input;

    if (!projectId || !nodeId || index === undefined) {
      return ctx.body = baseError.paramsError('缺少必要参数：projectId 或 nodeId');
    }

    try {
      const taskId = `${projectId}-${nodeId}-${index}`;
      const redisKey = `ai_edit_single:${taskId}`;

      // 检查任务是否存在
      const keyExists = await app.redis.exists(redisKey);
      if (!keyExists) {
        return ctx.body = baseError.dataNotExistError('任务不存在');
      }

      // 获取当前状态
      const currentValue = await app.redis.get(redisKey);
      logger.info(`[rerunAiEditTask] 当前状态: ${currentValue}`);

      // 如果状态是-3，直接返回失败
      if (currentValue === '-3') {
        return ctx.body = {
          status: 0,
          data: {
            task_id: taskId,
            message: '任务已被标记为最终失败状态，无法重跑',
            status: 'failed_connectivity',
          },
        };
      }

      // 获取项目信息
      const project = await service.project.base.getOne({ where: { id: projectId } });
      if (!project) {
        logger.error(`[rerunAiEditTask] 项目不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }

      // 获取项目元数据
      const projectMeta = await service.project.meta.getMetas({ projectId });

      // 获取项目下的所有books
      const { types } = service.book;
      const books = await service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      });

      // 找到类型为question的book
      const book = books.find((_book) => _book.type === types.question);
      if (!book) {
        logger.error(`[rerunAiEditTask] 项目试题图书不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目试题图书不存在');
      }

      // 获取book下的所有tasks
      const tasks = await service.task.base.getAll({
        where: { bookId: book.id },
        attributes: ['taskId', 'appKey', 'taskName', 'status', 'mergedTaskId'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      });

      if (!tasks || tasks.length === 0) {
        logger.error(`[rerunAiEditTask] 项目下没有任务: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目下没有任务');
      }

      // 使用第一个任务的ID查询文件信息
      const task = tasks[0];
      const file = await service.task.file.getFiles({ taskId: task.taskId });

      if (!file || !file.words || file.words.body.length === 0 && file.words.answer.length === 0) {
        logger.error(`[rerunAiEditTask] 项目文件不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目文件不存在');
      }

      // 获取 AI.json 中的对应节点用于连通性检查
      try {
        const aiJsonUrl = service.task.base.getAiUrl(project.appKey, taskId, 'ai.flatten.json', true);
        const itemJson: any[] = await service.oss.fetch(aiJsonUrl, 'json');

        // 检查 image_url 连通性
        async function checkImageUrlAvailable(url: string): Promise<boolean> {
          try {
            const res = await axios.head(url, { timeout: 3000 });
            return res.status === 200;
          } catch {
            return false;
          }
        }

        // 递归检查所有节点的 image_url
        let hasInvalidImageUrl = false;
        for (const { node } of iterateNode(itemJson)) {
          if (!node.image_url || typeof node.image_url !== 'string') {
            // 跳过空的image_url，不检查连通性
            continue;
          }
          const ok = await checkImageUrlAvailable(node.image_url);
          if (!ok) {
            hasInvalidImageUrl = true;
            break;
          }
        }

        if (hasInvalidImageUrl) {
          // 尝试重新运行转图
          try {
            logger.info(`[rerunAiEditTask] 检测到image_url连通性异常，尝试重新运行转图: ${taskId}`);

            // 设置状态为处理中
            await app.redis.set(redisKey, '0');

            // 先返回响应
            ctx.body = {
              status: 0,
              data: {
                task_id: taskId,
                message: '已启动后台任务重新生成图片，请稍后查询状态',
                status: 'processing',
              },
            };

            // 在后台运行转图
            ctx.runInBackground(async() => {
              try {
                await json2Jpg(itemJson);

                // 重新检查连通性
                let stillInvalid = false;
                for (const { node } of iterateNode(itemJson)) {
                  if (!node.image_url || typeof node.image_url !== 'string') {
                    // 跳过空的image_url，不检查连通性
                    continue;
                  }
                  const ok = await checkImageUrlAvailable(node.image_url);
                  if (!ok) {
                    stillInvalid = true;
                    break;
                  }
                }

                // 如果依然失败
                if (stillInvalid) {
                  await app.redis.set(redisKey, '-2');
                  logger.info(`[rerunAiEditTask:background] 重跑转图后依然连通性异常, redis置为-2: ${redisKey}`);

                  // 检查是否所有任务都完成
                  await this.checkAllTasksCompleted(app, projectId, project, logger);
                  return;
                }

                // 如果重跑转图修复了问题，重新上传修复后的json
                logger.info(`[rerunAiEditTask:background] 重跑转图成功修复了连通性问题: ${taskId}`);
                const jsonKey = service.task.base.getAiOssKey(project.appKey, taskId, 'ai.flatten.json');
                await service.oss.upload(jsonKey, JSON.stringify(itemJson), 'string');

                // 重新入队
                await this.reprocessTask(taskId, app, service, project, projectMeta, file, config, logger);
              } catch (jsonError) {
                logger.error(`[rerunAiEditTask:background] 重跑转图失败: ${jsonError}`);
                await app.redis.set(redisKey, '-2');

                // 检查是否所有任务都完成
                await this.checkAllTasksCompleted(app, projectId, project, logger);
              }
            });

            return;
          } catch (jsonError) {
            logger.error(`[rerunAiEditTask] 启动后台任务失败: ${jsonError}`);
            await app.redis.set(redisKey, '-2');

            // 检查是否所有任务都完成
            await this.checkAllTasksCompleted(app, projectId, project, logger);

            return ctx.body = {
              status: 0,
              data: {
                task_id: taskId,
                message: '启动后台任务失败，已标记为-2状态',
                status: 'failed_twice',
              },
            };
          }
        }
      } catch (error) {
        logger.error(`[rerunAiEditTask] 检查 image_url 失败: ${error}`);

        // 尝试重新获取json并运行转图
        try {
          logger.info(`[rerunAiEditTask] 尝试重新获取json并运行转图: ${taskId}`);

          // 设置状态为处理中
          await app.redis.set(redisKey, '0');

          // 先返回响应
          ctx.body = {
            status: 0,
            data: {
              task_id: taskId,
              message: '已启动后台任务重新获取JSON并生成图片，请稍后查询状态',
              status: 'processing',
            },
          };

          // 在后台运行重新获取并处理JSON
          ctx.runInBackground(async() => {
            try {
              const jsonUrl = service.task.base.getAiUrl(project.appKey, taskId, 'fixed.json', true);
              const itemJson = await service.oss.fetch(jsonUrl, 'json');

              // 转图
              await json2Jpg(itemJson);

              // 重新上传
              const jsonKey = service.task.base.getAiOssKey(project.appKey, taskId, 'ai.flatten.json');
              await service.oss.upload(jsonKey, JSON.stringify(itemJson), 'string');

              logger.info(`[rerunAiEditTask:background] 重新运行转图成功: ${taskId}`);

              // 重新入队
              await this.reprocessTask(taskId, app, service, project, projectMeta, file, config, logger);
            } catch (retryError) {
              logger.error(`[rerunAiEditTask:background] 重新获取json并运行转图失败: ${retryError}`);
              await app.redis.set(redisKey, '-2');

              // 检查是否所有任务都完成
              await this.checkAllTasksCompleted(app, projectId, project, logger);
            }
          });

          return;
        } catch (bgError) {
          logger.error(`[rerunAiEditTask] 启动后台任务失败: ${bgError}`);
          await app.redis.set(redisKey, '-2');

          // 检查是否所有任务都完成
          await this.checkAllTasksCompleted(app, projectId, project, logger);

          return ctx.body = {
            status: 0,
            data: {
              task_id: taskId,
              message: '启动后台任务失败，已标记为-2状态',
              status: 'failed_twice',
            },
          };
        }
      }

      // 正常情况下的重新入队
      await this.reprocessTask(taskId, app, service, project, projectMeta, file, config, logger);

      return ctx.body = {
        status: 0,
        data: {
          task_id: taskId,
          message: '任务重新入队成功',
          previous_fail_count: currentValue === '-1' ? 1 : (currentValue === '-2' ? 2 : 0),
        },
      };
    } catch (err) {
      logger.error(`[rerunAiEditTask] 错误: ${err}`);
      return ctx.body = baseError.serverError(`重新入队失败: ${err}`);
    }
  }

  /**
   * 重新处理任务（入队处理）
   * @private
   */
  private async reprocessTask(
    taskId: string,
    app: any,
    service: any,
    project: any,
    projectMeta: any,
    file: any,
    config: any,
    logger: any
  ) {
    const redisKey = `ai_edit_single:${taskId}`;
    const wordsArr = [...file.words.body, ...file.words.answer];
    const wordUrl = wordsArr[0].url;
    const projectId = taskId.split('-')[0];
    const htmlUrl = service.project.base.getUrl(project.appKey, projectId, 'html');

    // 获取当前状态，累计失败次数
    const currentValue = await app.redis.get(redisKey);
    let failCount = 0;
    if (currentValue === '-1') failCount = 1;
    else if (currentValue === '-2') failCount = 2;

    // 重置任务状态为未完成
    await app.redis.set(redisKey, '0');

    const stageMapping = {
      junior: 'cz',
      senior: 'gz',
    };

    // 定义学科映射
    const subjectMapping: any = {
      biology: 'shengwu',
      chemistry: 'huaxue',
      chinese: 'yuwen',
      computer_science: 'jisuanji',
      daode_fazhi: 'daofa',
      english: 'yingyu',
      geography: 'dili',
      history: 'lishi',
      math: 'shuxue',
      physics: 'wuli',
      science: 'kexue',
    };

    // 重新入队
    await service.rbs.initRBSQueue({
      task_id: taskId,
      task_type: 'ai_edit',
      task_info: {
        task_id: taskId,
        word_url: wordUrl,
        html_url: htmlUrl,
        json_url: service.task.base.getAiUrl(project.appKey, taskId, 'ai.flatten.json', true),
        app_key: project.appKey,
        subject: `${stageMapping[projectMeta.stage] || 'cz'}_${subjectMapping[(projectMeta.subject || project.subject) as any]}`
          || 'cz_yuwen',
        word_type: '',
        get_task_parameters: [
          'task_id',
          'word_url',
          'html_url',
          'json_url',
          'subject',
          'app_key',
          'word_type'
        ],
        callback_extras: [
          'task_id',
          'word_url',
          'html_url',
          'app_key',
          'subject',
          'word_type'
        ],
        run_type: 'common',
        callback_url: `${config.ngrok.callbackUrl}/api/open/task/proofreadingDocxSingle`,
        push_time: new Date().getTime(),
        timestamp: new Date().getTime(),
      },
    });

    // 添加超时任务
    await service.timeout.addTimeoutTask(
      taskId,
      'ai_edit_single',
      10.5 * 60 * 1000,
      {
        title: 'ai-edit任务超时',
        content: `任务ID: ${taskId} 的ai-edit任务执行超时，请检查ai-edit服务状态`,
        receiveId: 4,
        extra: service.ai.formatTaskParams({
          taskId,
          appKey: project.appKey,
          subject: projectMeta.subject || project.subject,
          stage: projectMeta.stage,
          wordType: '批量任务-子任务',
        }),
      }
    );

    logger.info(`[reprocessTask] 重新入队成功: ${taskId}, 之前失败次数: ${failCount}`);
    return failCount;
  }

  /**
   * 检查项目下所有任务是否都完成（状态为1或-3）
   * 如果都完成，则开始处理文档
   */
  private async checkAllTasksCompleted(app, projectId, project, logger) {
    try {
      const allKeys = await app.redis.keys(`ai_edit_single:${projectId}-*`);
      const allValues = await Promise.all(allKeys.map((key) => app.redis.get(key)));

      // 只有状态为1（成功）或-3（最终失败）才算完成
      const allCompleted = allValues.every((value) => value === '1' || value === '-3');

      if (allCompleted) {
        logger.info(`[checkAllTasksCompleted] 项目 ${projectId} 所有任务都已完成，开始处理文档`);

        try {
          // 获取 wordUrl
          const { service, config } = this;
          const { types } = service.book;

          // 获取项目下的所有 books
          const books = await service.book.getAll({
            where: { projectId },
            attributes: ['id', 'type'],
          });

          // 找到类型为 question 的 book
          const book = books.find((_book) => _book.type === types.question);
          if (!book) {
            logger.error(`[checkAllTasksCompleted] 项目试题图书不存在: ${projectId}`);
            return false;
          }

          // 获取 book 下的所有 tasks
          const tasks = await service.task.base.getAll({
            where: { bookId: book.id },
            attributes: ['taskId', 'appKey'],
            order: [['bookOrder', 'ASC'], ['id', 'ASC']],
          });

          if (!tasks || tasks.length === 0) {
            logger.error(`[checkAllTasksCompleted] 项目下没有任务: ${projectId}`);
            return false;
          }

          // 使用第一个任务的 ID 查询文件信息
          const task = tasks[0];
          const file = await service.task.file.getFiles({ taskId: task.taskId });

          if (!file || !file.words || file.words.body.length === 0 && file.words.answer.length === 0) {
            logger.error(`[checkAllTasksCompleted] 项目文件不存在: ${projectId}`);
            return false;
          }

          const wordsArr = [...file.words.body, ...file.words.answer];
          const wordUrl = wordsArr[0].url;

          // 所有 json 都处理完成，开始处理 word
          const manager = new WordAnnotationMannger(wordUrl,
            logger, `[checkAllTasksCompleted] projectId: ${projectId} `);
          await manager.loadDocument();

          // 处理 mode 节点
          function processModeNode(node: any) {
            manager.processErrorInfo(node);
            manager.processAgentInfo(node);
          }

          // 获取所有处理完成的 json
          const successfulJsons: any = (await Promise.all(allKeys.filter((key) =>
            app.redis.get(key).then((value) => value === '1')
          ).map(async(key) => {
            const taskId = key.replace('ai_edit_single:', '');
            const _jsonUrl = service.task.base.getAiUrl(project.appKey, taskId, 'ai.process.json', true);
            try {
              const jsonContent = await service.oss.fetch(_jsonUrl, 'json');
              return jsonContent;
            } catch (e) {
              logger.error(`[checkAllTasksCompleted] 获取 json 失败: ${_jsonUrl}, ${e}`);
              return null;
            }
          }))).filter(Boolean);
          // 过滤掉可能为 null 的结果

          logger.info(`[checkAllTasksCompleted] 获取所有处理完成的 json: ${successfulJsons.length} 个`);
          const allJsonData = successfulJsons.flat();

          // 上传所有处理好的 json
          const key = service.task.base.getAiOssKey(project.appKey, projectId, 'ai.process.json');
          await service.oss.upload(key, JSON.stringify(allJsonData), 'string');

          // 处理所有 json
          for (const item of allJsonData) {
            if (item.mode1 && !item.mode2) {
              for (const node of item.mode1) {
                processModeNode(node);
              }
            } else if (item.mode1 && item.mode2) {
              for (const node of item.mode1) {
                processModeNode(node);
              }
              for (const node of item.mode2) {
                processModeNode(node);
              }
            }
          }

          manager.removeAllCustomIdsAndMarkers();
          // 保存文档
          const docxBuffer = await manager.saveDocument();
          // 上传到 oss
          const docxKey = service.task.base.getAiOssKey(project.appKey, projectId, 'done.docx');
          await service.oss.upload(docxKey, docxBuffer);
          const wordResultUrl = service.task.base.getAiUrl(project.appKey, projectId, 'done.docx', true);
          logger.info(`[checkAllTasksCompleted] 上传成功: ${wordResultUrl}`);

          // 提交到工单交付物
          await app.curl(`${config.workOrder?.apiTask || config.workOrder.api}/api/open/project/v1/publish`, {
            method: 'POST',
            dataType: 'json',
            data: JSON.stringify({
              ticket_id: project?.workOrder || '',
              business_project_id: projectId,
              status: 0,
              result: {
                word_url: wordResultUrl,
                question_count: allJsonData.length,
              },
              reason: '',
            }),
          });

          logger.info(`[checkAllTasksCompleted] 提交工单交付物成功: ${JSON.stringify({
            ticket_id: project?.workOrder || '',
            business_project_id: projectId,
            status: 0,
            result: wordResultUrl,
          })}`);

          // 清理 redis
          await app.redis.del(...allKeys);
          logger.info('[checkAllTasksCompleted] 清理 redis 成功');

          return true;
        } catch (error) {
          logger.error(`[checkAllTasksCompleted] 处理文档失败: ${error}`);
          return false;
        }
      }

      return allCompleted;
    } catch (error) {
      logger.error(`[checkAllTasksCompleted] 检查项目完成状态失败: ${error}`);
      return false;
    }
  }

  /**
   * 重新处理项目的Word文档
   */
  public async reprocessWordDocument() {
    const { ctx, app, service, logger } = this;
    const { projectId } = ctx.input;

    if (!projectId) {
      return ctx.body = baseError.paramsError('缺少必要参数：projectId');
    }

    try {
      // 获取项目信息
      const project = await service.project.base.getOne({ where: { id: projectId } });
      if (!project) {
        logger.error(`[reprocessWordDocument] 项目不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目不存在');
      }

      // 获取项目的Word文档URL
      const { types } = service.book;
      const books = await service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      });

      // 找到类型为question的book
      const book = books.find((_book) => _book.type === types.question);
      if (!book) {
        logger.error(`[reprocessWordDocument] 项目试题图书不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目试题图书不存在');
      }

      // 获取book下的所有tasks
      const tasks = await service.task.base.getAll({
        where: { bookId: book.id },
        attributes: ['taskId', 'appKey', 'taskName', 'status', 'mergedTaskId'],
        order: [['bookOrder', 'ASC'], ['id', 'ASC']],
      });

      if (!tasks || tasks.length === 0) {
        logger.error(`[reprocessWordDocument] 项目下没有任务: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目下没有任务');
      }

      // 使用第一个任务的ID查询文件信息
      const task = tasks[0];
      const file = await service.task.file.getFiles({ taskId: task.taskId });

      if (!file || !file.words || (file.words.body.length === 0 && file.words.answer.length === 0)) {
        logger.error(`[reprocessWordDocument] 项目文件不存在: ${projectId}`);
        return ctx.body = baseError.dataNotExistError('项目文件不存在');
      }

      // 获取Word URL
      const wordsArr = [...file.words.body, ...file.words.answer];
      const wordUrl = wordsArr[0].url;

      // 从OSS获取处理好的JSON
      let jsonData;
      try {
        const jsonUrl = service.task.base.getAiUrl(project.appKey, projectId, 'ai.process.json', true);
        jsonData = await service.oss.fetch(jsonUrl, 'json');
        logger.info(`[reprocessWordDocument] 获取JSON成功: ${jsonUrl}`);
      } catch (error) {
        logger.error(`[reprocessWordDocument] 获取JSON失败: ${error}`);
        return ctx.body = baseError.serverError(`获取JSON失败，可能是处理尚未完成: ${error}`);
      }

      // 处理Word文档
      try {
        const manager = new WordAnnotationMannger(wordUrl, logger,
          `[reprocessWordDocument] word 处理中：taskId ${projectId} `);
        await manager.loadDocument();

        // 处理mode节点
        function processModeNode(node: any) {
          manager.processErrorInfo(node);
          manager.processAgentInfo(node);
        }

        // 处理所有JSON数据
        for (const item of jsonData) {
          if (item.mode1 && !item.mode2) {
            for (const node of item.mode1) {
              try {
                processModeNode(node);
              } catch (error) {
                logger.error(`[reprocessWordDocument] 处理节点失败: ${node.id}, ${error}`);
              }
            }
          } else if (item.mode1 && item.mode2) {
            for (const node of item.mode1) {
              try {
                processModeNode(node);
              } catch (error) {
                logger.error(`[reprocessWordDocument] 处理节点失败: ${node.id}, ${error}`);
              }
            }
            for (const node of item.mode2) {
              try {
                processModeNode(node);
              } catch (error) {
                logger.error(`[reprocessWordDocument] 处理节点失败: ${node.id}, ${error}`);
              }
            }
          }
        }

        manager.removeAllCustomIdsAndMarkers();

        // 保存文档
        const docxBuffer = await manager.saveDocument();

        // 上传到OSS
        const docxKey = service.task.base.getAiOssKey(project.appKey, projectId, 'done.docx');
        await service.oss.upload(docxKey, docxBuffer);
        const wordResultUrl = service.task.base.getAiUrl(project.appKey, projectId, 'done.docx', true);

        logger.info(`[reprocessWordDocument] 上传成功: ${wordResultUrl}`);

        // 提交到工单交付物
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            ticket_id: project?.workOrder || '',
            business_project_id: projectId,
            status: 0,
            result: {
              word_url: wordResultUrl,
              question_count: jsonData.length,
            },
            reason: '',
          }),
        });

        return ctx.body = {
          status: 0,
          data: {
            projectId,
            wordUrl: wordResultUrl,
            message: 'Word文档重新处理成功',
          },
        };
      } catch (error) {
        logger.error(`[reprocessWordDocument] 处理Word文档失败: ${error}`);
        return ctx.body = baseError.serverError(`处理Word文档失败: ${error}`);
      }
    } catch (error) {
      logger.error(`[reprocessWordDocument] 错误: ${error}`);
      return ctx.body = baseError.serverError(`重新处理Word文档失败: ${error}`);
    }
  }

  /**
   * 处理HTML转Word回调
   */
  public async html2wordCallback() {
    const { ctx, service, logger, app } = this;
    const { projectId: taskId, result, status, reason } = ctx.input;

    logger.info(`[html2wordCallback] 接收回调: ${JSON.stringify(ctx.input)}`);
    try {
      if (status === 3) {
        logger.error(`[html2wordCallback] 转换失败: taskId: ${taskId}, reason: ${reason}`);
        const errorData = {
          type: 'error',
          receive_id: 25,
          server: { name: 'AI-EDIT 流程监控' },
          content: {
            title: 'HTML转Word任务失败',
            text: `任务失败通知\n
任务ID: ${taskId}`,
          },
        };
        await service.robot.sendRobotMessage(errorData);
        await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
          method: 'POST',
          dataType: 'json',
          data: JSON.stringify({
            ticket: taskId,
            business_project_id: taskId,
            status: 1,
            result: {},
            reason: reason || '未知错误',
          }),
        });
        return ctx.body = { status: 0 };
      }

      // 从taskId中移除_html2word后缀
      const [originalTaskId, appkey] = taskId.split('_');

      await service.timeout.releaseTimeoutTask(taskId, 'html_docx');
      // 转换成功，上传到OSS
      const docxKey = service.task.base.getAiOssKey(appkey, originalTaskId, 'wps.docx');

      // 从result下载docx并上传到我们的OSS
      const response = await axios.get(result, { responseType: 'arraybuffer' });
      await service.oss.upload(docxKey, response.data);

      const docxUrl = service.task.base.getAiUrl(appkey, originalTaskId, 'wps.docx', true);
      logger.info(`[html2wordCallback] Word文档上传成功: ${docxUrl}`);
      await this.service.rbs.callbackRBS({
        task_id: taskId,
        task_type: 'html_docx',
        task_status: true,
      });

      // 更新工单交付物，增加Word文档链接
      await app.curl(`${this.config.workOrder?.apiTask || this.config.workOrder.api}/api/open/project/v1/publish`, {
        method: 'POST',
        dataType: 'json',
        data: JSON.stringify({
          ticket_id: originalTaskId,
          business_project_id: originalTaskId,
          status: 0,
          reason: '',
          result: {
            word_url: docxUrl,
            question_count: 1,
          },
        }),
      });

      return ctx.body = { status: 0 };
    } catch (err) {
      logger.error(`[html2wordCallback] 处理失败: ${err}`);
      return ctx.body = baseError.serverError(`html2wordCallback处理失败: ${err}`);
    }
  }
}
