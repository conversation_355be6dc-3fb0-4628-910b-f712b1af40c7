/**
 * @file 原始图片操作
 */

'use strict';

import { Controller } from 'egg';
import { ESourceImageStatus } from '../../model/sourceImage';
import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';

export default class SourceImageController extends Controller {

  public async getPreCropTaskList() {
    const { service, ctx } = this;
    const { statuses } = service.task.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const key: string = ctx.input.key;
    const isOutAdmin: boolean = ctx.input.isOutAdmin;
    const where = { key, status: statuses.preCropChecked };
    const [count, tasks] = await Promise.all(!isOutAdmin ? [
      service.task.base.relatedCount({ where }),
      service.task.base.getRelatedList({
        page,
        pageSize,
        where,
        order: [['id', 'desc']],
      })
    ] : [
      service.task.base.exactCount({ where }),
      service.task.base.getExactList({
        page,
        pageSize,
        where,
        order: [['id', 'desc']],
      })
    ]);

    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        list: tasks,
      },
    };
  }

  @validate({ taskId: 'number' })
  public async getPreCropImages() {
    const { ctx, service } = this;
    const { taskId } = ctx.input as { taskId: number };
    const images = await service.sourceImage.getAll({ where: { taskId } });
    images.forEach((item) => {
      const url = service.image.getUrl(item.appKey, item.imageId, 'jpg');
      const info = item.info ? JSON.parse(item.info) : undefined;
      const result = item.result ? JSON.parse(item.result) : [];
      Object.assign(item, { url, info, result });
    });
    ctx.body = { status: 0, data: images };
  }

  @validate({ taskId: 'number' })
  public async restartPreCrop() {
    // 重新手工切图
    const { ctx, service, app } = this;
    const { userId } = ctx.data;
    const { taskId } = ctx.input as { taskId: number };
    const { statuses } = service.task.base;
    const taskExists = await service.task.base.exists({
      where: {
        taskId,
        status: [
          statuses.columnAutoProcessed, statuses.columnProcessing, statuses.columnProcessed,
          statuses.unmarked
        ],
        rerun: 0,
      },
    });
    if (!taskExists) {
      return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
    }
    const existsImage = await service.sourceImage.exists({ where: { taskId } });
    if (!existsImage) {
      return ctx.body = baseError.dataNotExistError('任务不存在原图');
    }
    await app.model.transaction(async(transaction) => {
      await service.image.delete({ transaction, where: { taskId } });
      await service.task.base.update({
        status: statuses.preCropChecked,
        preprocessUserId: 0,
        markUserId: 0,
      }, { transaction, where: { taskId } });
      await service.sourceImage.update({ status: ESourceImageStatus.init }, { transaction, where: { taskId } });
    });

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.restart.id,
        data: '',
        costTime: 0,
      });
      await service.task.base.deleteTaskConfig(taskId, 'imageColumnProcessor');
      await service.task.base.deleteTaskConfig(taskId, 'imageStructProcessor');
    });
    ctx.body = { status: 0 };
  }

  @validate({
    taskId: 'number',
    data: {
      type: 'array',
      required: false,
      itemType: 'object',
      rule: {
        imageId: 'string',
        result: {
          type: 'array',
          required: false,
          itemType: 'object',
          rule: {
            x: 'number',
            y: 'number',
            w: 'number',
            h: 'number',
            i: 'number?',
          },
          min: 0,
          max: 8,
        },
      },
      min: 1,
    },
  })
  public async submitPreCrop() {
    // 提交手工切图结果
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    const { taskId, data } = ctx.input as {
      taskId: number;
      data: { // 每个图片的切图结果
        imageId: string;
        result?: { x: number, y: number, w: number, h: number, i?: number }[];
      }[];
    };
    const res = await service.sourceImage.submitPreCrop(taskId, data);
    if (res.type === 0 || res.type === 1) {
      return ctx.body = res.msg;
    }
    //
    // const task = await service.task.base.getOne({
    //   where: { taskId, status: statuses.preCropChecked },
    //   attributes: ['id'],
    // });
    // if (!task) {
    //   return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
    // }
    // const existImageCount = await service.sourceImage.count({
    //   where: { imageId: data.map(item => item.imageId), taskId },
    // });
    // if (existImageCount !== data.length) {
    //   return ctx.body = baseError.dataNotExistError('部分图片不存在');
    // }
    // // 保存图片结果，更新图片状态
    // // @todo：这里建议用 bulkCreate 方法重写，优化性能！
    // for (let d = 0; d < data.length; d += 1) {
    //   await service.sourceImage.update(
    //       { status: ESourceImageStatus.finished, result: data[d].result?.length ? JSON.stringify(data[d].result) : '' },
    //       { where: { imageId: data[d].imageId, taskId } });
    // }
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.submit.id,
        data: JSON.stringify(data),
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async finishPreCrop() {
    // 完成手工切图。系统将按提交结果对图片切割。
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // const { statuses } = service.task.base;
    const { taskId } = ctx.input as { taskId: number };
    const res = await service.sourceImage.finishTask(taskId);
    if (res) {
      ctx.body = res;
    }
    /*
     * const [affectedCount] = await service.task.base.update(
     *   { status: statuses.preCropProcessed },
     *   { where: { taskId, status: statuses.preCropChecked } },
     * );
     * if (!affectedCount) {
     *   return ctx.body = baseError.dataNotExistError('任务不存在或状态错误');
     * }
     * await service.task.base.pushPreCrop(taskId);
     */

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.task.history.create({
        taskId,
        userId,
        type: service.task.history.preCropTypes.finish.id,
        data: '',
        costTime: 0,
      });

      // 更新 stat
      const task = await service.task.base.getOne({ where: { taskId } });
      const images = await service.sourceImage.getAll({ where: { taskId } });
      await service.task.stat.newStat({
        html: '',
        taskId,
        type: 'pre_crop',
        appKey: task!.appKey,
        subject: task!.subject,
        imageCount: 0,
        resourceType: task!.resourceType!,
        userId,
        preCropCount: images.length,
      });
    });

    ctx.body = { status: 0 };
  }

  @validate({ appKey: 'string', imageId: 'string', angle: 'number' })
  public async rotateImage() {
    const { ctx, service } = this;
    const { appKey, imageId, angle } = ctx.input as { appKey: string, imageId: string, angle: 90 | -90 | 180 };
    await service.task.base.rotatePreImage(appKey, imageId, angle);

    ctx.body = { status: 0 };
  }

  @validate(['number'])
  public async batchFinishPreCrop() {
    const { ctx, service } = this;
    const { userId } = ctx.data;
    // 重新写一个方法从数据库一次获取到request body中任务的所有图片信息
    const taskIds = Object.values(ctx.input as number[]);
    const res = await service.sourceImage.batchFinishPreCrop(taskIds, ctx.userId);
    res.forEach((taskInfo) => {
      const { taskId, imageLength, task } = taskInfo;
      // 记录操作日志
      ctx.runInBackground(async() => {
        await service.task.history.create({
          taskId: taskInfo.taskId,
          userId,
          type: service.task.history.preCropTypes.finish.id,
          data: '',
          costTime: 0,
        });

        // 更新 stat
        await service.task.stat.newStat({
          html: '',
          taskId,
          type: 'pre_crop',
          appKey: task!.appKey,
          subject: task!.subject,
          imageCount: 0,
          resourceType: task!.resourceType!,
          userId,
          preCropCount: imageLength,
        });
      });
    });
    ctx.body = { status: 0 , data: res };
  }

  public async getFinishPreCropTaskList() {
    const { service, ctx } = this;
    const taskId = ctx.input.taskId;
    const where = {taskId: Number(taskId)};
    const tasks = await service.sourceImage.getList({
      where,
      order: [['id', 'desc']],
    });

    ctx.body = {
      status: 0,
      data: {
        list: tasks.map((v) => ({
          createTime: v.createTime,
          originImageUrl: service.image.getUrl(v.appKey, v.imageId, 'jpg'),
          taskId: v.taskId,
          result: v.result,
          info: v.info,
        })),
      },
    };
  }
}
