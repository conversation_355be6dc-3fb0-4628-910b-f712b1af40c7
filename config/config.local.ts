/**
 * @file 本地环境配置文件
 * <AUTHOR>
 */

'use strict';

import { EggAppConfig, PowerPartial } from 'egg';

import EnvConfig from '../typings/config/default';
import { BaseError } from '../app/core/base/errors';

export type DefaultConfig = PowerPartial<EggAppConfig> & EnvConfig.CustomEnv & EnvConfig.DefaultEnv;

const config = {
  /*
   * redis配置
   * redis: {
   *   client: {
   *     port: 6379,
   *     host: '127.0.0.1',
   *     password: '',
   *     db: 22,
   *   },
   * },
   */
  redis: {
    client: {
      port: 6379,
      host: 'r-uf6c1chzshionnoflppd.redis.rds.aliyuncs.com',
      password: 'sigmaLOVE2017',
      db: 9,
    },
  },
  /*
   * mysql 配置
   * sequelize: {
   *   dialect: 'mysql',
   *   database: 'xdoc',
   *   username: 'root',
   *   password: 'Password1',
   *   host: '127.0.0.1',
   *   port: 3306,
   *   logging: true,
   *   timezone: '+08:00',
   *   pool: {
   *     max: 30,
   *     min: 0,
   *     idle: 10000,
   *   },
   *   define: {
   *     engine: 'InnoDB',
   *     timestamps: false,
   *     createdAt: 'createTime',
   *     updatedAt: 'updateTime',
   *     charset: 'utf8',
   *   },
   * },
   */
  sequelize: {
    dialect: 'mysql',
    database: 'xdoc',
    username: 'sigma',
    password: 'sigmaLOVE2017',
    host: 'rm-uf68040g28501oyn1vo.mysql.rds.aliyuncs.com',
    port: 3306,
    logging: true,
    timezone: '+08:00',
    pool: {
      max: 100,
      min: 0,
      idle: 10000,
    },
    define: {
      engine: 'InnoDB',
      timestamps: false,
      createdAt: 'createTime',
      updatedAt: 'updateTime',
      charset: 'utf8',
    },
  },
  /*
   * mongodb配置
   * mongo: {
   *   client: {
   *     uri: 'mongodb://127.0.0.1',
   *     db: 'xdoc'
   *   },
   * },
   */
  mongo: {
    client: {
      uri: 'mongodb://root:<EMAIL>:3717,dds-uf6fcc4e461ee5a42194-pub.mongodb.rds.aliyuncs.com:3717/admin?replicaSet=mgset-7558683',
      db: 'xdoc',
    },
  },
  // 阿里云的oss的存储配置
  aliOss: {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'xdoc-stable',
    host: 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/',
    privateHost: 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/',
  },
  // b端账户系统
  uc: {
    api: 'http://uc.hexinedu.com/api',
    appKey: 'a5a421eaa0c3f453680bee38',
    appSecret: 'cb7be849afc81fd34dabcfa16c8e7585bda19141',
  },
  // B端权限系统
  themis: {
    appKey: 'themis63250c4628',
    appSecret: '4dd2ef63e16e6589584dcb11b73598ed',
    api: 'http://themis.hexinedu.com/api/open',
  },
  // 唯一id的生成服务
  idOnly: {
    api: 'http://only.hexinedu.com/api',
    taskId: {
      appKey: 'xdoc_task_id_dev',
      appSecret: 'd69c3227443e98ccc59879dafb1ea17e',
      maxSizePerTimes: 1000,
    },
  },
  // 新内容服务
  content: { api: 'http://content-server.hexinedu.com/api/content' },
  // rpc服务
  remoteRpcServer: {
    imageCrop: {
      host: 'tcp://*************:80',
      method: 'crop_img',
      timeout: 20,
    },
    imageCropRerun: {
      host: 'tcp://*************:8888',
      method: 'crop_img',
      timeout: 20,
    },
  },
  wordRbs: { initQueueUrl: 'http://rbs-word.hexin.im/api/proxy/word/init' },
  taskV2Rbs: {
    initQueueWordUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/word/init',
    initQueueFbdUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/fbd/init',
    removeQueueUrlTask: 'http://rbs-founder.hexinedu.com/api/proxy/task/remove',
    initQueueWordUrlNewTask: 'http://rbs-founder.hexinedu.com/api/proxy/task/init',

    initQueueWordUrl: 'http://*************/api/proxy/word/init',
    initQueueFbdUrl: 'http://*************/api/proxy/fbd/init',
    removeQueueUrl: 'http://*************/api/proxy/task/remove',
    initQueueWordUrlNew: 'http://rbs-founder.hexinedu.com/api/proxy/task/init',
  },
  // 绕开 mac 没有权限写 /data/logs/xdoc 问题
  logger: {
    dir: './logs/xdoc',
    consoleLevel: 'DEBUG',
  },
  python: { bin: 'python3' },
  // json预处理
  jsonPreprocess: { api: 'http://jsonpreprocess.hexinedu.com' },
  workOrder: {
    bucket: 'hexin-worksheet',
    api: 'http://worksheet.hexinedu.com',
    fileSysPath: '/files-dev',
  },
  // solr服务
  solr: {
    host: 'http://solr.hexinedu.com',
    pdfSearch: '/solr/pdf_search/select',
  },
  ngrok: { callbackUrl: 'https://upward-gibbon-genuinely.ngrok-free.app' },
} as DefaultConfig;

config.onerror = {
  json(err, ctx) {
    // 可在控制台快速定位源码
    console.error(err);
    if (err instanceof BaseError) {
      ctx.body = err.asBaseError();
    } else {
      ctx.body = {
        status: 1005,
        statusInfo: err.message || '未知错误',
      };
    }
    ctx.status = 200;
  },
  html(err, ctx) {
    // 可在控制台快速定位源码
    console.error(err);
    if (err instanceof BaseError) {
      ctx.body = JSON.stringify(err.asBaseError());
    } else {
      ctx.body = JSON.stringify({
        status: 1005,
        statusInfo: err.message || '未知错误',
      });
    }
    ctx.status = 200;
  },
};

export default config;
